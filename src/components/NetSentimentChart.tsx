import React from "react";

interface SurveyResult {
  statement: string;
  responses: Record<string, number>;
  net_sentiment: number;
  avg_score: number;
}

interface ConceptStatement {
  labels: string[];
  statement: string;
}

interface NetSentimentChartProps {
  data: SurveyResult[];
  conceptStatements: ConceptStatement[];
}

export const NetSentimentChart: React.FC<NetSentimentChartProps> = ({
  data,
  conceptStatements,
}) => {
  // Get category labels from concept statements for context-aware sentiment calculation
  const categoryLabels =
    conceptStatements.length > 0 ? conceptStatements[0].labels : [];

  // Dynamic sentiment color calculation based on scale context
  const getSentimentColor = (sentiment: number, scaleSize: number) => {
    // Adjust thresholds based on scale size
    const highThreshold = scaleSize <= 3 ? 30 : scaleSize <= 5 ? 40 : 50;
    const moderateThreshold = scaleSize <= 3 ? 10 : scaleSize <= 5 ? 15 : 20;

    if (sentiment >= highThreshold) return "text-green-600"; // High positive
    if (sentiment >= moderateThreshold) return "text-green-500"; // Moderate positive
    if (sentiment >= 0) return "text-green-400"; // Low positive
    return "text-red-500"; // Negative
  };

  const formatSentiment = (sentiment: number) => {
    return sentiment > 0
      ? `+${sentiment.toFixed(0)}%`
      : `${sentiment.toFixed(0)}%`;
  };

  // Dynamic grid layout based on number of statements
  const getGridLayout = (numStatements: number) => {
    if (numStatements <= 2) return "grid-cols-1 md:grid-cols-2";
    if (numStatements <= 3) return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
    if (numStatements <= 4) return "grid-cols-1 md:grid-cols-2 lg:grid-cols-4";
    return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"; // For 5+ statements, use 3 columns max
  };

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 lg:p-8">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-800 text-center mb-6">
          NET SENTIMENT
        </h2>

        <div className={`grid ${getGridLayout(data.length)} gap-4 md:gap-6`}>
          {data.map((item, index) => (
            <div
              key={index}
              className="bg-gray-50 rounded-lg border border-gray-200 p-4 md:p-6"
            >
              <div className="text-center">
                <h3 className="text-sm md:text-base font-semibold text-gray-700 mb-3 md:mb-4 leading-tight">
                  {item.statement}
                </h3>

                <div
                  className={`text-3xl md:text-4xl lg:text-5xl font-bold mb-2 ${getSentimentColor(
                    item.net_sentiment,
                    categoryLabels.length
                  )}`}
                >
                  {formatSentiment(item.net_sentiment)}
                </div>

                {/* Show average score as additional context */}
                <div className="text-xs md:text-sm text-gray-500 mt-2">
                  Avg: {item.avg_score.toFixed(2)}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Scale context information */}
        {categoryLabels.length > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="text-center">
              <p className="text-xs md:text-sm text-gray-600">
                Based on {categoryLabels.length}-point scale:{" "}
                {categoryLabels.join(" • ")}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
