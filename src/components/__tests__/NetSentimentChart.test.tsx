import React from "react";
import { render, screen } from "@testing-library/react";
import { NetSentimentChart } from "../NetSentimentChart";

// Test data based on the provided backend response
const testData = {
  conceptStatements: [
    {
      labels: [
        "Strongly Disagree",
        "Disagree", 
        "Neutral",
        "Agree",
        "Strongly Agree",
      ],
      statement: "Would taste great",
    },
  ],
  surveyResults: [
    {
      avg_score: 3.83,
      net_sentiment: 58,
      responses: {
        Agree: 41,
        Disagree: 8,
        Neutral: 18,
        "Strongly Agree": 29,
        "Strongly Disagree": 4,
      },
      statement: "Would taste great",
    },
    {
      avg_score: 3.59,
      net_sentiment: 41,
      responses: {
        Agree: 36,
        Disagree: 13,
        Neutral: 23,
        "Strongly Agree": 23,
        "Strongly Disagree": 5,
      },
      statement: "Would have good texture",
    },
  ],
};

// Test data with 3 categories
const testDataThreeCategories = {
  conceptStatements: [
    {
      labels: ["Poor", "Good", "Excellent"],
      statement: "Overall quality rating",
    },
  ],
  surveyResults: [
    {
      avg_score: 2.1,
      net_sentiment: 15,
      responses: {
        Poor: 45,
        Good: 35,
        Excellent: 20,
      },
      statement: "Overall quality rating",
    },
  ],
};

// Test data with 7 categories
const testDataSevenCategories = {
  conceptStatements: [
    {
      labels: [
        "Extremely Unlikely",
        "Very Unlikely",
        "Unlikely",
        "Neutral",
        "Likely",
        "Very Likely",
        "Extremely Likely",
      ],
      statement: "Likelihood to purchase",
    },
  ],
  surveyResults: [
    {
      avg_score: 4.2,
      net_sentiment: 35,
      responses: {
        "Extremely Unlikely": 5,
        "Very Unlikely": 8,
        Unlikely: 12,
        Neutral: 25,
        Likely: 30,
        "Very Likely": 15,
        "Extremely Likely": 5,
      },
      statement: "Likelihood to purchase",
    },
  ],
};

describe("NetSentimentChart", () => {
  test("renders with standard 5-category data", () => {
    render(
      <NetSentimentChart
        data={testData.surveyResults}
        conceptStatements={testData.conceptStatements}
      />
    );

    expect(screen.getByText("NET SENTIMENT")).toBeInTheDocument();
    expect(screen.getByText("Would taste great")).toBeInTheDocument();
    expect(screen.getByText("Would have good texture")).toBeInTheDocument();
    expect(screen.getByText("+58%")).toBeInTheDocument();
    expect(screen.getByText("+41%")).toBeInTheDocument();
    expect(screen.getByText("Avg: 3.83")).toBeInTheDocument();
    expect(screen.getByText("Avg: 3.59")).toBeInTheDocument();
  });

  test("shows scale context information", () => {
    render(
      <NetSentimentChart
        data={testData.surveyResults}
        conceptStatements={testData.conceptStatements}
      />
    );

    expect(screen.getByText("Based on 5-point scale:")).toBeInTheDocument();
    expect(screen.getByText(/Strongly Disagree • Disagree • Neutral • Agree • Strongly Agree/)).toBeInTheDocument();
  });

  test("handles 3-category data correctly", () => {
    render(
      <NetSentimentChart
        data={testDataThreeCategories.surveyResults}
        conceptStatements={testDataThreeCategories.conceptStatements}
      />
    );

    expect(screen.getByText("Overall quality rating")).toBeInTheDocument();
    expect(screen.getByText("+15%")).toBeInTheDocument();
    expect(screen.getByText("Based on 3-point scale:")).toBeInTheDocument();
    expect(screen.getByText(/Poor • Good • Excellent/)).toBeInTheDocument();
  });

  test("handles 7-category data correctly", () => {
    render(
      <NetSentimentChart
        data={testDataSevenCategories.surveyResults}
        conceptStatements={testDataSevenCategories.conceptStatements}
      />
    );

    expect(screen.getByText("Likelihood to purchase")).toBeInTheDocument();
    expect(screen.getByText("+35%")).toBeInTheDocument();
    expect(screen.getByText("Based on 7-point scale:")).toBeInTheDocument();
  });

  test("handles negative sentiment correctly", () => {
    const negativeData = [
      {
        avg_score: 2.1,
        net_sentiment: -25,
        responses: {
          "Strongly Disagree": 30,
          Disagree: 25,
          Neutral: 20,
          Agree: 15,
          "Strongly Agree": 10,
        },
        statement: "Would recommend",
      },
    ];

    render(
      <NetSentimentChart
        data={negativeData}
        conceptStatements={testData.conceptStatements}
      />
    );

    expect(screen.getByText("-25%")).toBeInTheDocument();
  });

  test("renders empty state gracefully", () => {
    render(<NetSentimentChart data={[]} conceptStatements={[]} />);

    expect(screen.getByText("NET SENTIMENT")).toBeInTheDocument();
  });

  test("handles many statements with proper grid layout", () => {
    const manyStatementsData = Array.from({ length: 6 }, (_, i) => ({
      avg_score: 3.0 + i * 0.1,
      net_sentiment: 20 + i * 5,
      responses: {
        "Strongly Disagree": 5 + i,
        Disagree: 10 + i,
        Neutral: 20 + i,
        Agree: 35 - i,
        "Strongly Agree": 30 - i,
      },
      statement: `Test statement ${i + 1}`,
    }));

    render(
      <NetSentimentChart
        data={manyStatementsData}
        conceptStatements={testData.conceptStatements}
      />
    );

    expect(screen.getByText("Test statement 1")).toBeInTheDocument();
    expect(screen.getByText("Test statement 6")).toBeInTheDocument();
    expect(screen.getByText("+20%")).toBeInTheDocument();
    expect(screen.getByText("+45%")).toBeInTheDocument();
  });
});
