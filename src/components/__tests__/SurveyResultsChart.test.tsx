import React from "react";
import { render, screen } from "@testing-library/react";
import { SurveyResultsChart } from "../SurveyResultsChart";

// Test data based on the provided backend response
const testData = {
  conceptStatements: [
    {
      labels: [
        "Strongly Disagree",
        "Disagree",
        "Neutral",
        "Agree",
        "Strongly Agree",
      ],
      statement: "Would taste great",
    },
    {
      labels: [
        "Strongly Disagree",
        "Disagree",
        "Neutral",
        "Agree",
        "Strongly Agree",
      ],
      statement: "Would have good texture",
    },
  ],
  surveyResults: [
    {
      avg_score: 3.83,
      net_sentiment: 58,
      responses: {
        Agree: 41,
        Disagree: 8,
        Neutral: 18,
        "Strongly Agree": 29,
        "Strongly Disagree": 4,
      },
      statement: "Would taste great",
    },
    {
      avg_score: 3.59,
      net_sentiment: 41,
      responses: {
        Agree: 36,
        Disagree: 13,
        Neutral: 23,
        "Strongly Agree": 23,
        "Strongly Disagree": 5,
      },
      statement: "Would have good texture",
    },
  ],
};

// Test data with different category labels (3 categories)
const testDataThreeCategories = {
  conceptStatements: [
    {
      labels: ["Poor", "Good", "Excellent"],
      statement: "Overall quality rating",
    },
  ],
  surveyResults: [
    {
      avg_score: 2.1,
      net_sentiment: 15,
      responses: {
        Poor: 45,
        Good: 35,
        Excellent: 20,
      },
      statement: "Overall quality rating",
    },
  ],
};

// Test data with more categories (7 categories)
const testDataSevenCategories = {
  conceptStatements: [
    {
      labels: [
        "Extremely Unlikely",
        "Very Unlikely",
        "Unlikely",
        "Neutral",
        "Likely",
        "Very Likely",
        "Extremely Likely",
      ],
      statement: "Likelihood to purchase",
    },
  ],
  surveyResults: [
    {
      avg_score: 4.2,
      net_sentiment: 35,
      responses: {
        "Extremely Unlikely": 5,
        "Very Unlikely": 8,
        Unlikely: 12,
        Neutral: 25,
        Likely: 30,
        "Very Likely": 15,
        "Extremely Likely": 5,
      },
      statement: "Likelihood to purchase",
    },
  ],
};

// Test data with very small percentages to test visibility
const testDataSmallPercentages = {
  conceptStatements: [
    {
      labels: [
        "Strongly Disagree",
        "Disagree",
        "Neutral",
        "Agree",
        "Strongly Agree",
      ],
      statement: "Would recommend to others",
    },
  ],
  surveyResults: [
    {
      avg_score: 4.1,
      net_sentiment: 75,
      responses: {
        "Strongly Disagree": 1, // 1% - should test visibility
        Disagree: 2, // 2% - should test visibility
        Neutral: 7, // 7% - should test visibility
        Agree: 40, // 40% - should be visible
        "Strongly Agree": 50, // 50% - should be visible
      },
      statement: "Would recommend to others",
    },
  ],
};

describe("SurveyResultsChart", () => {
  test("renders with standard 5-category data", () => {
    render(
      <SurveyResultsChart
        data={testData.surveyResults}
        conceptStatements={testData.conceptStatements}
      />
    );

    expect(screen.getByText("SURVEY RESULTS")).toBeInTheDocument();
    expect(screen.getByText("Would taste great")).toBeInTheDocument();
    expect(screen.getByText("Would have good texture")).toBeInTheDocument();
    expect(screen.getByText("Avg: 3.83")).toBeInTheDocument();
    expect(screen.getByText("Avg: 3.59")).toBeInTheDocument();
  });

  test("renders legend with indexed numbers", () => {
    render(
      <SurveyResultsChart
        data={testData.surveyResults}
        conceptStatements={testData.conceptStatements}
      />
    );

    expect(screen.getByText("[1] Strongly Disagree")).toBeInTheDocument();
    expect(screen.getByText("[2] Disagree")).toBeInTheDocument();
    expect(screen.getByText("[3] Neutral")).toBeInTheDocument();
    expect(screen.getByText("[4] Agree")).toBeInTheDocument();
    expect(screen.getByText("[5] Strongly Agree")).toBeInTheDocument();
  });

  test("handles 3-category data correctly", () => {
    render(
      <SurveyResultsChart
        data={testDataThreeCategories.surveyResults}
        conceptStatements={testDataThreeCategories.conceptStatements}
      />
    );

    expect(screen.getByText("Overall quality rating")).toBeInTheDocument();
    expect(screen.getByText("[1] Poor")).toBeInTheDocument();
    expect(screen.getByText("[2] Good")).toBeInTheDocument();
    expect(screen.getByText("[3] Excellent")).toBeInTheDocument();
    expect(screen.getByText("Avg: 2.10")).toBeInTheDocument();
  });

  test("handles 7-category data correctly", () => {
    render(
      <SurveyResultsChart
        data={testDataSevenCategories.surveyResults}
        conceptStatements={testDataSevenCategories.conceptStatements}
      />
    );

    expect(screen.getByText("Likelihood to purchase")).toBeInTheDocument();
    expect(screen.getByText("[1] Extremely Unlikely")).toBeInTheDocument();
    expect(screen.getByText("[7] Extremely Likely")).toBeInTheDocument();
    expect(screen.getByText("Avg: 4.20")).toBeInTheDocument();
  });

  test("handles small percentages correctly", () => {
    render(
      <SurveyResultsChart
        data={testDataSmallPercentages.surveyResults}
        conceptStatements={testDataSmallPercentages.conceptStatements}
      />
    );

    expect(screen.getByText("Would recommend to others")).toBeInTheDocument();
    expect(screen.getByText("Avg: 4.10")).toBeInTheDocument();
  });

  test("renders empty state gracefully", () => {
    render(<SurveyResultsChart data={[]} conceptStatements={[]} />);

    expect(screen.getByText("SURVEY RESULTS")).toBeInTheDocument();
  });

  test("handles many statements correctly", () => {
    const manyStatementsData = Array.from({ length: 8 }, (_, i) => ({
      avg_score: 3.0 + i * 0.1,
      net_sentiment: 20 + i * 5,
      responses: {
        "Strongly Disagree": 5 + i,
        Disagree: 10 + i,
        Neutral: 20 + i,
        Agree: 35 - i,
        "Strongly Agree": 30 - i,
      },
      statement: `Test statement ${i + 1}`,
    }));

    const conceptStatements = Array.from({ length: 8 }, (_, i) => ({
      labels: [
        "Strongly Disagree",
        "Disagree",
        "Neutral",
        "Agree",
        "Strongly Agree",
      ],
      statement: `Test statement ${i + 1}`,
    }));

    render(
      <SurveyResultsChart
        data={manyStatementsData}
        conceptStatements={conceptStatements}
      />
    );

    expect(screen.getByText("Test statement 1")).toBeInTheDocument();
    expect(screen.getByText("Test statement 8")).toBeInTheDocument();
    expect(screen.getByText("Avg: 3.00")).toBeInTheDocument();
    expect(screen.getByText("Avg: 3.70")).toBeInTheDocument();
  });

  test("percentage calculations are correct", () => {
    const testData = [
      {
        avg_score: 3.0,
        net_sentiment: 20,
        responses: {
          "Strongly Disagree": 10, // 10%
          Disagree: 20, // 20%
          Neutral: 30, // 30%
          Agree: 25, // 25%
          "Strongly Agree": 15, // 15%
        },
        statement: "Percentage test",
      },
    ];

    const conceptStatements = [
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Percentage test",
      },
    ];

    render(
      <SurveyResultsChart
        data={testData}
        conceptStatements={conceptStatements}
      />
    );

    expect(screen.getByText("Percentage test")).toBeInTheDocument();
    expect(screen.getByText("Avg: 3.00")).toBeInTheDocument();
  });
});
