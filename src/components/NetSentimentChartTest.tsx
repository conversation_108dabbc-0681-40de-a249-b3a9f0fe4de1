import React, { useState } from "react";
import { NetSentimentChart } from "./NetSentimentChart";

// Test data scenarios to demonstrate dynamic behavior
const testScenarios = {
  standard: {
    name: "Standard 5-Category Scale",
    conceptStatements: [
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Would taste great",
      },
    ],
    surveyResults: [
      {
        avg_score: 3.83,
        net_sentiment: 58,
        responses: {
          "Strongly Disagree": 4,
          Disagree: 8,
          Neutral: 18,
          Agree: 41,
          "Strongly Agree": 29,
        },
        statement: "Would taste great",
      },
      {
        avg_score: 3.59,
        net_sentiment: 41,
        responses: {
          "Strongly Disagree": 5,
          Disagree: 13,
          Neutral: 23,
          Agree: 36,
          "Strongly Agree": 23,
        },
        statement: "Would have good texture",
      },
      {
        avg_score: 3.72,
        net_sentiment: 52,
        responses: {
          "Strongly Disagree": 3,
          Disagree: 10,
          Neutral: 20,
          Agree: 38,
          "Strongly Agree": 29,
        },
        statement: "Would be worth the price",
      },
      {
        avg_score: 3.45,
        net_sentiment: 35,
        responses: {
          "Strongly Disagree": 8,
          Disagree: 15,
          Neutral: 20,
          Agree: 32,
          "Strongly Agree": 25,
        },
        statement: "Would recommend to friends",
      },
    ],
  },

  threeCategory: {
    name: "3-Category Scale",
    conceptStatements: [
      {
        labels: ["Poor", "Good", "Excellent"],
        statement: "Overall quality rating",
      },
    ],
    surveyResults: [
      {
        avg_score: 2.1,
        net_sentiment: 15,
        responses: {
          Poor: 45,
          Good: 35,
          Excellent: 20,
        },
        statement: "Overall quality rating",
      },
      {
        avg_score: 2.3,
        net_sentiment: 25,
        responses: {
          Poor: 35,
          Good: 40,
          Excellent: 25,
        },
        statement: "Value for money",
      },
      {
        avg_score: 1.9,
        net_sentiment: 5,
        responses: {
          Poor: 50,
          Good: 35,
          Excellent: 15,
        },
        statement: "Brand reputation",
      },
    ],
  },

  sevenCategory: {
    name: "7-Category NPS-style Scale",
    conceptStatements: [
      {
        labels: [
          "Extremely Unlikely",
          "Very Unlikely",
          "Unlikely",
          "Neutral",
          "Likely",
          "Very Likely",
          "Extremely Likely",
        ],
        statement: "Likelihood to purchase",
      },
    ],
    surveyResults: [
      {
        avg_score: 4.2,
        net_sentiment: 35,
        responses: {
          "Extremely Unlikely": 5,
          "Very Unlikely": 8,
          Unlikely: 12,
          Neutral: 25,
          Likely: 30,
          "Very Likely": 15,
          "Extremely Likely": 5,
        },
        statement: "Likelihood to purchase",
      },
      {
        avg_score: 4.8,
        net_sentiment: 55,
        responses: {
          "Extremely Unlikely": 3,
          "Very Unlikely": 5,
          Unlikely: 7,
          Neutral: 15,
          Likely: 35,
          "Very Likely": 25,
          "Extremely Likely": 10,
        },
        statement: "Likelihood to recommend",
      },
    ],
  },

  negativeResults: {
    name: "Negative Sentiment Example",
    conceptStatements: [
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Would purchase again",
      },
    ],
    surveyResults: [
      {
        avg_score: 2.1,
        net_sentiment: -35,
        responses: {
          "Strongly Disagree": 25,
          Disagree: 30,
          Neutral: 20,
          Agree: 15,
          "Strongly Agree": 10,
        },
        statement: "Would purchase again",
      },
      {
        avg_score: 1.8,
        net_sentiment: -45,
        responses: {
          "Strongly Disagree": 35,
          Disagree: 25,
          Neutral: 20,
          Agree: 12,
          "Strongly Agree": 8,
        },
        statement: "Would recommend to others",
      },
    ],
  },
};

export const NetSentimentChartTest: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState<keyof typeof testScenarios>("standard");

  const currentScenario = testScenarios[selectedScenario];

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
          NetSentimentChart Dynamic Testing
        </h1>
        
        {/* Scenario Selector */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Select Test Scenario:</h2>
          <div className="flex flex-wrap gap-4">
            {Object.entries(testScenarios).map(([key, scenario]) => (
              <button
                key={key}
                onClick={() => setSelectedScenario(key as keyof typeof testScenarios)}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  selectedScenario === key
                    ? "bg-blue-500 text-white border-blue-500"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                }`}
              >
                {scenario.name}
              </button>
            ))}
          </div>
        </div>

        {/* Current Scenario Info */}
        <div className="mb-6 p-4 bg-white rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold mb-2">Current Scenario: {currentScenario.name}</h3>
          <p className="text-gray-600">
            Scale: {currentScenario.conceptStatements[0].labels.length}-point scale
          </p>
          <p className="text-gray-600">
            Labels: {currentScenario.conceptStatements[0].labels.join(" → ")}
          </p>
          <p className="text-gray-600">
            Statements: {currentScenario.surveyResults.length}
          </p>
        </div>

        {/* NetSentimentChart */}
        <NetSentimentChart
          data={currentScenario.surveyResults}
          conceptStatements={currentScenario.conceptStatements}
        />

        {/* Data Preview */}
        <div className="mt-8 p-4 bg-white rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Data Preview</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Statement</th>
                  <th className="text-left p-2">Avg Score</th>
                  <th className="text-left p-2">Net Sentiment</th>
                  <th className="text-left p-2">Color Threshold</th>
                </tr>
              </thead>
              <tbody>
                {currentScenario.surveyResults.map((item, index) => {
                  const scaleSize = currentScenario.conceptStatements[0].labels.length;
                  const highThreshold = scaleSize <= 3 ? 30 : scaleSize <= 5 ? 40 : 50;
                  const moderateThreshold = scaleSize <= 3 ? 10 : scaleSize <= 5 ? 15 : 20;
                  
                  let colorCategory = "Negative";
                  if (item.net_sentiment >= highThreshold) colorCategory = "High Positive";
                  else if (item.net_sentiment >= moderateThreshold) colorCategory = "Moderate Positive";
                  else if (item.net_sentiment >= 0) colorCategory = "Low Positive";

                  return (
                    <tr key={index} className="border-b">
                      <td className="p-2">{item.statement}</td>
                      <td className="p-2">{item.avg_score.toFixed(2)}</td>
                      <td className="p-2">{item.net_sentiment > 0 ? '+' : ''}{item.net_sentiment}%</td>
                      <td className="p-2">{colorCategory}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};
