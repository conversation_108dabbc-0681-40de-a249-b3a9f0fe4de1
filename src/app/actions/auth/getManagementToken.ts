"use server";

import axios from "axios";
import * as Sen<PERSON> from "@sentry/nextjs";

interface TokenCache {
  token: string | null;
  expiresAt: number | null;
}

interface Auth0TokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope?: string;
}

// In-memory cache for the token
let tokenCache: TokenCache = {
  token: null,
  expiresAt: null,
};

/**
 * Gets an Auth0 Management API token, using a cached version if available
 * @returns {Promise<string>} The Auth0 Management API token
 */
export async function getManagementToken(): Promise<string> {
  // Check if we have a valid cached token
  if (
    tokenCache.token &&
    tokenCache.expiresAt &&
    Date.now() < tokenCache.expiresAt
  ) {
    return tokenCache.token;
  }

  // If no valid token in cache, fetch a new one
  try {
    // Validate required environment variables
    const clientId = process.env.SUBCONSCIOUSAI_M2M_CLIENT_ID;
    const clientSecret = process.env.SUBCONSCIOUSAI_M2M_CLIENT_SECRET;
    const audience = process.env.AUTH0_AUDIENCE;

    if (!clientId || !clientSecret || !audience) {
      throw new Error(
        "Missing required environment variables for Auth0 authentication: " +
          (!clientId ? "SUBCONSCIOUSAI_M2M_CLIENT_ID " : "") +
          (!clientSecret ? "SUBCONSCIOUSAI_M2M_CLIENT_SECRET " : "") +
          (!audience ? "AUTH0_AUDIENCE" : "")
      );
    }

    const options = {
      method: "POST",
      url: "https://dev-5qhuxyzkmd8cku6i.us.auth0.com/oauth/token",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      data: new URLSearchParams({
        grant_type: "client_credentials",
        client_id: clientId,
        client_secret: clientSecret,
        audience,
        scope: "read:users read:users_app_metadata",
      }),
    };

    const response = await axios.request<Auth0TokenResponse>(options);
    const token = response.data.access_token;
    const expiresIn = response.data.expires_in * 1000; // Convert to milliseconds

    // Cache the token with a small buffer (5 minutes) before expiration
    tokenCache = {
      token,
      expiresAt: Date.now() + expiresIn - 300000, // 5 minutes buffer
    };

    return token;
  } catch (error) {
    console.error("Error retrieving Auth0 management token:", error);

    // Log error to Sentry with context
    Sentry.captureException(error, {
      tags: {
        action: "getManagementToken",
        source: "server_action",
      },
      extra: {
        endpoint: "https://dev-5qhuxyzkmd8cku6i.us.auth0.com/oauth/token",
        errorMessage: error instanceof Error ? error.message : String(error),
      },
    });

    throw new Error("Failed to retrieve Auth0 management token");
  }
}
