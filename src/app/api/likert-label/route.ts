import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApi<PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";

interface LikertLabelRequest {
  description: string;
  image_name: string;
  scale: string; // Keep as string based on image, even if empty
  statements: string[];
}

export const POST = withApiAuthRequired(async function POST(req: NextRequest) {
  const res = new NextResponse(); // Create response object internally
  try {
    // Pass req and res to getAccessToken
    const { accessToken } = await getAccessToken(req, res);
    if (!accessToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body: LikertLabelRequest = await req.json();

    // Validate request body
    if (
      !body.description ||
      !body.image_name ||
      !body.statements ||
      !Array.isArray(body.statements) ||
      body.statements.length === 0
    ) {
      return NextResponse.json(
        { error: "Missing required fields in request body" },
        { status: 400 }
      );
    }

    const backendUrl = `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/experiments/likert-label`;

    const backendResponse = await axios.post(backendUrl, body, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    // Return backend data using the internally created response object for potential cookie setting
    return NextResponse.json(backendResponse.data, res);
  } catch (error: unknown) {
    console.error("Likert Label API Error:", error);
    const status = axios.isAxiosError(error) ? error.response?.status : 500;
    const message = axios.isAxiosError(error)
      ? error.response?.data?.detail || error.message
      : "Internal Server Error";
    // Use a new response object for errors
    return NextResponse.json(
      { error: "Failed to generate Likert labels", details: message },
      { status: status || 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
