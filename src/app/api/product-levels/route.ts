import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withA<PERSON><PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";

export const POST = withApiAuthRequired(async function POST(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: { params?: Record<string, string | string[]> } // Allow params to be optional
) {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Parse and validate request body
    const body = await req.json();
    if (!body.why_prompt || !body.country) {
      return NextResponse.json(
        { error: "why_prompt and country are required" },
        { status: 400 }
      );
    }

    // Make API call with improved error handling
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/product-attributes-levels`,
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 120_000, // 2 minutes (using numeric separator)
        validateStatus: () => true,
      }
    );

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error(
      "Product Levels Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    if (axios.isAxiosError(error)) {
      if (error.code === "ECONNABORTED") {
        return NextResponse.json(
          { error: "Request timed out. Please try again." },
          { status: 408 }
        );
      }
      return NextResponse.json(
        {
          error: "Failed to generate product levels",
          details: error.response?.data || error.message,
        },
        { status: error.response?.status || 500 }
      );
    }

    return NextResponse.json(
      { error: "An unknown error occurred" },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
