import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import {
  DisplayAttribute,
  DisplayTrait,
  Level,
} from "../../_components/_ideation/objects";

function filterActiveAttributesAndLevels(
  displayAttributes: DisplayAttribute[]
) {
  const filteredAttributes = displayAttributes.filter(
    (attribute: DisplayAttribute) => {
      return (
        attribute.active === true &&
        attribute.levels.filter((level: Level) => level.active).length >= 2
      );
    }
  );
  return filteredAttributes.map((attribute: DisplayAttribute) => {
    return [
      attribute.attribute,
      attribute.levels
        .filter((level: Level) => level.active)
        .map((level: Level) => level.level),
      attribute.attribute_type,
    ];
  });
}

function filterActiveTraits(displayTraits: DisplayTrait[]) {
  const activeTraits = displayTraits.filter(
    (trait: DisplayTrait) => trait.active
  );
  const formattedTraits: { [key: string]: string[] } = {};

  activeTraits.forEach((trait: DisplayTrait) => {
    formattedTraits[trait.title] = trait.values;
  });

  return formattedTraits;
}

export const POST = withApiAuthRequired(async function POST(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: {} // Required for Next.js 15+ compatibility
) {
  try {
    const { accessToken } = await getAccessToken(req, new NextResponse());
    const body = await req.json();

    const experimentRequest = {
      why_prompt: body.question,
      year: body.year,
      country: body.country,
      is_private: body.is_private,
      experiment_type: body.experiment_type,
      image_name: body.image_name,
      concept_description: body.concept_description,
      concept_statements: body.concept_statements,
      external_personas:
        body.external_personas && body.external_personas.length > 0
          ? body.external_personas
          : [],
      population_traits: filterActiveTraits(body.population_traits),
      pre_cooked_attributes_and_levels_lookup: filterActiveAttributesAndLevels(
        body.displayAttributes
      ),
      realworld_products: body.realworld_products,
      target_population:
        body.target_population && Object.keys(body.target_population).length > 0
          ? body.target_population
          : {},
    };
    console.log("body.target_population", body.target_population);
    console.log(experimentRequest);
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/experiments`,
      experimentRequest,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 10_000, // 10 seconds (using numeric separator)
        validateStatus: () => true,
      }
    );

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error(
      "Experiment Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    return NextResponse.json(
      {
        error: "Failed to create experiment",
        details: axios.isAxiosError(error)
          ? error.response?.data || error.message
          : error instanceof Error
            ? error.message
            : "Unknown error",
      },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
