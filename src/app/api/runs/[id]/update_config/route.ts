import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withA<PERSON><PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const PUT = withApiAuthRequired(async function PUT(
  req: NextRequest,
  context: { params?: Record<string, string | string[]> } // Adjusted context typing for compatibility
) {
  try {
    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());

    // Get ID from route parameters
    const runID = context.params?.id;

    if (!runID) {
      return NextResponse.json({ error: "Missing run ID" }, { status: 400 });
    }

    // Parse and validate request body
    const requestBody = await req.json();
    const isPrivate = requestBody?.config_update?.set_privacy;

    if (typeof isPrivate !== "boolean") {
      return NextResponse.json(
        { error: "Invalid privacy setting" },
        { status: 400 }
      );
    }

    // Make API call with improved error handling
    const { data } = await axios.put(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${runID}/update_config`,
      {
        config_update: {
          set_privacy: isPrivate,
        },
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 10_000, // 10 seconds (using numeric separator)
        validateStatus: () => true,
      }
    );

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error(
      "Config Update Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          error: ErrorMessage.privacyStatus,
          details: error.response?.data || error.message,
        },
        { status: error.response?.status || 500 }
      );
    }

    return NextResponse.json(
      { error: ErrorMessage.privacyStatus },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
