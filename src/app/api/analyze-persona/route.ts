import { NextRequest, NextResponse } from "next/server";
import { generateObject, generateText } from "ai";
import { createOpenAI } from "@ai-sdk/openai";
import { z } from "zod";

const openai = createOpenAI({
  // custom settings, e.g.
  apiKey: process.env.OPENAI_API_KEY || "",
});

export const runtime = "edge"; // Use Edge runtime for better performance

export async function POST(req: NextRequest) {
  try {
    const { content, extractMultiple = false } = await req.json();

    if (!content) {
      return NextResponse.json(
        { error: "No content provided" },
        { status: 400 }
      );
    }

    if (extractMultiple) {
      // Prepare the system prompt for multiple personas
      const systemPrompt = `
        You are an AI assistant specializing in user persona creation from unstructured text.
Analyze the provided document meticulously to extract structured information and generate MULTIPLE distinct user personas if they are clearly described. Create a separate persona object for each identified individual.

Your primary goal is to capture not only demographic details but also **deeper insights into personality traits and behaviors**.
- **Personality Traits:** Look for descriptions of character, disposition, attitudes, and temperament (e.g., 'optimistic', 'cautious', 'tech-savvy', 'risk-averse', 'introverted', 'detail-oriented'). **Infer these traits** from stated opinions, reactions, motivations, and descriptive language used in the text, even if not explicitly labeled as a trait.
- **Behaviors:** Identify recurring actions, habits, decision-making patterns, communication styles, preferences, and interactions mentioned in the text (e.g., 'prefers email communication', 'researches extensively before purchasing', 'enjoys collaborative projects', 'frequently travels', 'avoids conflict'). **Infer behaviors** from described activities, routines, and responses to situations.

You MUST return a valid JSON array of persona objects adhering strictly to the following structure:
{
"personas": [
  {
    "name": "string", // Extracted or inferred name
    "age": "string", // Extracted or inferred age/range
    "gender": "string", // Extracted or inferred gender
    "maritalStatus": "string", // e.g., Single, Married, Divorced
    "income": "string", // e.g., Range, specific amount, descriptor like 'High', 'Average'
    "education": "string", // Highest level achieved or field of study
    "racialGroup": "string", // If mentioned
    "homeOwnership": "string", // e.g., Rents, Owns
    "vehiclesOwned": "string", // Type or number of vehicles
    "hasDrivingLicense": "string", // Yes/No/Unknown
    "location": "string", // City, State, Country, Region
    "occupation": "string", // Job title or field
    "background": "string", // Relevant history, upbringing, experiences
    "goals": "string", // Stated or inferred objectives, aspirations
    "painPoints": "string", // Challenges, frustrations, problems faced
    "personalityTraits": "string", // Comma-separated list of inferred/stated traits
    "behaviors": "string" // Comma-separated list of inferred/stated behaviors and habits
  },
  // ... potentially more persona objects
]
}

Guidelines:
1.  If information for a specific field is not present or cannot be reasonably inferred from the text, use an empty string "" for that field.
2.  Only create personas for individuals who are distinctly characterized in the document. Aim for multiple personas if the text supports it.
3.  Ensure the output is ONLY the JSON array. Do not include any introductory text, explanations, apologies, or summaries outside the JSON structure itself.
4.  Focus on extracting *both* explicitly stated and implicitly suggested traits and behaviors based on the overall context provided in the document.
      `;

      // User prompt with the document content
      const userPrompt = `
        Document to analyze:
        ${content}
        
        Extract multiple personas information and return them as a JSON array of persona objects.
      `;

      const { object } = await generateObject({
        system: systemPrompt,
        prompt: userPrompt,
        model: openai("gpt-4o-mini"),
        schema: z.object({
          personas: z.array(
            z.object({
              name: z.string().nullish(),
              age: z.string().nullish(),
              gender: z.string().nullish(),
              maritalStatus: z.string().nullish(),
              income: z.string().nullish(),
              education: z.string().nullish(),
              racialGroup: z.string().nullish(),
              homeOwnership: z.string().nullish(),
              vehiclesOwned: z.string().nullish(),
              hasDrivingLicense: z.string().nullish(),
              location: z.string().nullish(),
              occupation: z.string().nullish(),
              background: z.string().nullish(),
              goals: z.string().nullish(),
              painPoints: z.string().nullish(),
              personalityTraits: z.string().nullish(),
              behaviors: z.string().nullish(),
            })
          ),
        }),
      });
      return NextResponse.json(object);
    } else {
      // Original single persona extraction
      const systemPrompt = `
        You are an AI assistant that extracts structured information from text to create user personas.
        Analyze the provided document and extract information to create a comprehensive user persona.
        You must return a valid JSON object with the following structure:
        {
          "name": "string",
          "age": "string",
          "gender": "string",
          "maritalStatus": "string",
          "income": "string",
          "education": "string",
          "racialGroup": "string",
          "homeOwnership": "string",
          "vehiclesOwned": "string",
          "hasDrivingLicense": "string",
          "location": "string",
          "occupation": "string",
          "background": "string",
          "goals": "string",
          "painPoints": "string",
          "personalityTraits": "string",
          "behaviors": "string"
        }
        
        If any information is not present in the document, use an empty string for that field.
        Do not include any explanations or additional text outside the JSON object.
      `;

      // User prompt with the document content
      const userPrompt = `
        Document to analyze:
        ${content}
        
        Extract the persona information and return it as a JSON object.
      `;

      const { object } = await generateObject({
        system: systemPrompt,
        prompt: userPrompt,
        model: openai("gpt-4o-mini"),
        schema: z.object({
          name: z.string().nullish(),
          age: z.string().nullish(),
          gender: z.string().nullish(),
          maritalStatus: z.string().nullish(),
          income: z.string().nullish(),
          education: z.string().nullish(),
          racialGroup: z.string().nullish(),
          homeOwnership: z.string().nullish(),
          vehiclesOwned: z.string().nullish(),
          hasDrivingLicense: z.string().nullish(),
          location: z.string().nullish(),
          occupation: z.string().nullish(),
          background: z.string().nullish(),
          goals: z.string().nullish(),
          painPoints: z.string().nullish(),
          personalityTraits: z.string().nullish(),
          behaviors: z.string().nullish(),
        }),
      });
      return NextResponse.json(object);
    }
  } catch (error) {
    console.error("Error in analyze-persona API:", error);
    return NextResponse.json(
      { error: "Failed to analyze persona document" },
      { status: 500 }
    );
  }
}
