import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApi<PERSON>uthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";

export const POST = withApiAuthRequired(async function POST(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: {} // Required for Next.js 15+ compatibility
) {
  try {
    const { accessToken } = await getAccessToken(req, new NextResponse());

    const body = await req.json();
    const why_prompt = body.why_prompt;

    if (!why_prompt) {
      return NextResponse.json(
        { error: "why_prompt is required" },
        { status: 400 }
      );
    }

    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/market-simulator/check-realworld-product`,
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        timeout: 10_000, // 10 seconds timeout
        validateStatus: () => true,
      }
    );

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error("Verification Error:", error);

    if (axios.isAxiosError(error)) {
      if (error.code === "ECONNABORTED") {
        return NextResponse.json(
          { error: "Request timed out. Please try again." },
          { status: 408 }
        );
      }
      if (error.response) {
        return NextResponse.json(
          { error: error.response.data?.error || "Verification failed" },
          { status: error.response.status }
        );
      }
    }

    return NextResponse.json(
      { error: "Unable to verify product existence in the real world" },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 compatibility fix
