import React, { useContext, useState } from "react";
import ConceptTestingContext from "./ConceptTestingContext";
import {
  CheckCircle,
  Image as ImageIcon,
  HelpCircle,
  ListChecks,
  AlertCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useUser } from "@auth0/nextjs-auth0/client";
import NavigationButtons from "./NavigationButtons";
import PublicExperimentQuestionModal from "../_ideation/PublicExperimentQuestionModal";
import { useValidation } from "../_ideation/_who/contexts/ValidationContext";
import { LogRunExperimentEvent } from "../_util/Analytics";
import Image from "next/image";

interface FinalReviewStepProps {
  onBack: () => void;
}

const FinalReviewStep: React.FC<FinalReviewStepProps> = ({ onBack }) => {
  const [showModal, setShowModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitResult, setSubmitResult] = useState<string | null>(null);
  const [isSubmitError, setIsSubmitError] = useState(false);
  const router = useRouter();
  const { user } = useUser();
  const context = useContext(ConceptTestingContext);
  const { selectedPopulationTraits } = useValidation();

  const {
    image,
    imageS3Url,
    description,
    questions,
    when,
    where,
    selectedState,
    displayTraits,
    personas,
    imageS3Key,
  } = context;

  const validQuestions = questions.filter(
    (q) => q.text && q.text.trim() !== ""
  );

  const handleShowPrivacyModal = () => {
    setShowModal(true);
  };

  const handleSetExperimentPrivate = () => {
    setShowModal(false);
    handleRunExperimentWithPrivacy(true);
  };

  const handleSetExperimentPublic = () => {
    setShowModal(false);
    handleRunExperimentWithPrivacy(false);
  };

  const handleModalClose = () => {
    setShowModal(false);
  };

  const handleRunExperimentWithPrivacy = async (privateValue: boolean) => {
    setIsSubmitting(true);
    setSubmitResult(null);
    setIsSubmitError(false);
    try {
      const payload: any = {
        question: description,
        year: when,
        country: where?.name,
        is_private: privateValue,
        experiment_type: "concept_testing",
        state:
          where?.name === "United States of America (USA)"
            ? selectedState
            : null,
        image_name: imageS3Key || "",
        concept_description: description,
        concept_statements: validQuestions.map((q) => ({
          statement: q.text,
          labels:
            q.scale && q.scale.length > 0
              ? q.scale
              : [
                  "Strongly Disagree",
                  "Disagree",
                  "Neutral",
                  "Agree",
                  "Strongly Agree",
                ],
        })),
        ...(personas.length > 0
          ? { external_personas: personas }
          : { target_population: selectedPopulationTraits?.population_traits }),
        population_traits: displayTraits,
        displayAttributes: [],
        realworld_products: [],
      };
      console.log("Sending payload to API:", payload);

      const response = await fetch("/api/experiments", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.details || errorData.error || "Failed to run concept test"
        );
      }

      const resultData = await response.json();
      setSubmitResult(
        resultData.message || "Concept test submitted successfully!"
      );
      setIsSubmitError(false);

      const experimentId = resultData.wandb_run_id;
      if (experimentId) {
        // Update local storage to track running concept tests (mirror ideation page logic)
        if (typeof window !== "undefined") {
          let runningConceptTests =
            JSON.parse(localStorage.getItem("runningConceptTests") || "0") + 1;
          localStorage.setItem(
            "runningConceptTests",
            JSON.stringify(runningConceptTests)
          );
          localStorage.setItem("pendingConceptTestRunId", experimentId);
        }

        // Log analytics event (mirror ideation page logic)
        <LogRunExperimentEvent email={user?.email} />;

        // Navigate to concept testing results page (mirror experiments flow)
        router.push("/concept-testing/results");
        return;
      }
    } catch (err: any) {
      setSubmitResult(err.message || "An error occurred");
      setIsSubmitError(true);
      toast.error(
        err.message || "An error occurred while submitting the concept test."
      );
    } finally {
      setIsSubmitting(false);
      setShowModal(false);
    }
  };

  return (
    <div className="flex flex-col gap-6 max-w-4xl w-full mx-auto">
      <h2 className="text-xl font-semibold text-text-default">Final Review</h2>
      <p className="text-text-light mb-4">
        Review your concept test details before launching the concept test.
      </p>

      {submitResult && (
        <div
          className={`p-4 border rounded-lg mb-6 flex items-center gap-2 ${
            isSubmitError
              ? "border-red-200 bg-red-50"
              : "border-green-200 bg-green-50"
          }`}
        >
          {isSubmitError ? (
            <AlertCircle className="h-5 w-5 text-red-500" />
          ) : (
            <CheckCircle className="h-5 w-5 text-green-500" />
          )}
          <span
            className={`font-medium ${
              isSubmitError ? "text-red-700" : "text-green-700"
            }`}
          >
            {submitResult}
          </span>
        </div>
      )}

      {/* Content Card */}
      <div className="bg-white border border-input rounded-lg overflow-hidden shadow-sm">
        {/* Image and Description Section */}
        <div className="p-6 border-b border-input">
          <h3 className="text-base font-medium text-text-default mb-4">
            Concept Details
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Image Section */}
            <div>
              <div className="text-sm font-medium text-text-default mb-2">
                Concept Image
              </div>
              {image ? (
                <div className="relative rounded-md overflow-hidden border border-input flex justify-center">
                  {/* Use local object URL for preview */}
                  <Image
                    width={500}
                    height={300}
                    src={URL.createObjectURL(image)}
                    alt="Concept Preview"
                    className="max-w-full max-h-48 w-auto h-auto object-contain"
                  />
                </div>
              ) : imageS3Url ? (
                <div className="relative rounded-md overflow-hidden border border-input flex justify-center">
                  {/* Fallback to S3 URL if local blob is gone */}
                  <Image
                    width={500}
                    height={300}
                    src={imageS3Url}
                    alt="Concept Preview"
                    className="max-w-full max-h-48 w-auto h-auto object-contain"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center h-48 bg-muted/20 rounded-md border border-input">
                  <ImageIcon className="h-10 w-10 text-muted-foreground" />
                </div>
              )}
            </div>

            {/* Description Section */}
            <div>
              <div className="text-sm font-medium text-text-default mb-2">
                Description
              </div>
              <div className="p-3 bg-muted/20 rounded-md border border-input min-h-[150px] text-sm">
                {description || "No description provided."}
              </div>
            </div>
          </div>
        </div>

        {/* When & Where Section */}
        <div className="p-6 border-b border-input">
          <h3 className="text-base font-medium text-text-default mb-4">
            Time & Location
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="text-sm font-medium text-text-default mb-2">
                When
              </div>
              <div className="p-3 bg-muted/20 rounded-md border border-input text-sm">
                {when || "Not specified"}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-text-default mb-2">
                Where
              </div>
              <div className="p-3 bg-muted/20 rounded-md border border-input text-sm flex items-center gap-2">
                {where?.flag && (
                  <Image
                    src={where.flag}
                    alt={`${where.name} flag`}
                    width={24}
                    height={16}
                    className="inline-block mr-1"
                  />
                )}
                {where?.name || "Not specified"}
              </div>
            </div>
          </div>
        </div>

        {/* Questions Section - Updated to show scale */}
        <div className="p-6 border-b border-input">
          <h3 className="text-base font-medium text-text-default mb-4 flex items-center gap-2">
            <ListChecks className="h-5 w-5" /> Questions & Scales
          </h3>

          {validQuestions.length > 0 ? (
            <div className="space-y-4">
              {validQuestions.map((q) => (
                <div
                  key={q.id} // Use unique ID
                  className="p-4 bg-muted/20 rounded-md border border-input"
                >
                  <p className="text-sm font-medium mb-2">{q.text}</p>
                  {q.scale && q.scale.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {q.scale.map((label: string, labelIdx: number) => (
                        <Badge
                          key={labelIdx}
                          variant="secondary"
                          className="font-normal"
                        >
                          {label}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <Badge
                      variant="outline"
                      className="text-muted-foreground font-normal"
                    >
                      Scale not generated
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center text-muted-foreground bg-muted/20 rounded-md border border-input">
              <HelpCircle className="h-6 w-6 mx-auto mb-2" />
              <p>No questions added.</p>
            </div>
          )}
        </div>

        {/* WHO Section - Population and Traits */}
        <div className="p-6 border-b border-input">
          <h3 className="text-base font-medium text-text-default mb-4">
            Population Characteristics
          </h3>

          {/* Population Demographics */}
          {selectedPopulationTraits?.population_traits && (
            <div className="mb-6">
              <h4 className="text-sm font-medium text-text-default mb-3">
                Demographics
                {selectedPopulationTraits && (
                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                    (Population Size: {selectedPopulationTraits.population_size}
                    )
                  </span>
                )}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Use selectedPopulationTraits if available, otherwise fall back to populationTraits */}
                {(() => {
                  const traits = selectedPopulationTraits?.population_traits;
                  return (
                    <>
                      {traits.age &&
                        Array.isArray(traits.age) &&
                        traits.age.length === 2 && (
                          <div className="p-3 bg-muted/20 rounded-md border border-input">
                            <span className="text-xs font-medium text-text-default">
                              Age Range:
                            </span>
                            <p className="text-sm">
                              {traits.age[0]} - {traits.age[1]} years
                            </p>
                          </div>
                        )}
                      {traits.household_income &&
                        Array.isArray(traits.household_income) &&
                        traits.household_income.length === 2 && (
                          <div className="p-3 bg-muted/20 rounded-md border border-input">
                            <span className="text-xs font-medium text-text-default">
                              Household Income:
                            </span>
                            <p className="text-sm">
                              ${traits.household_income[0].toLocaleString()} - $
                              {traits.household_income[1].toLocaleString()}
                            </p>
                          </div>
                        )}
                      {traits.gender &&
                        Array.isArray(traits.gender) &&
                        traits.gender.length > 0 && (
                          <div className="p-3 bg-muted/20 rounded-md border border-input">
                            <span className="text-xs font-medium text-text-default">
                              Gender:
                            </span>
                            <p className="text-sm">
                              {traits.gender.join(", ")}
                            </p>
                          </div>
                        )}
                      {traits.education_level &&
                        Array.isArray(traits.education_level) &&
                        traits.education_level.length > 0 && (
                          <div className="p-3 bg-muted/20 rounded-md border border-input">
                            <span className="text-xs font-medium text-text-default">
                              Education:
                            </span>
                            <p className="text-sm">
                              {traits.education_level.join(", ")}
                            </p>
                          </div>
                        )}
                      {traits.number_of_children &&
                        Array.isArray(traits.number_of_children) &&
                        traits.number_of_children.length > 0 && (
                          <div className="p-3 bg-muted/20 rounded-md border border-input">
                            <span className="text-xs font-medium text-text-default">
                              Number of Children:
                            </span>
                            <p className="text-sm">
                              {traits.number_of_children.join(", ")}
                            </p>
                          </div>
                        )}
                      {traits.racial_group &&
                        Array.isArray(traits.racial_group) &&
                        traits.racial_group.length > 0 && (
                          <div className="p-3 bg-muted/20 rounded-md border border-input">
                            <span className="text-xs font-medium text-text-default">
                              Racial Group:
                            </span>
                            <p className="text-sm">
                              {traits.racial_group.join(", ")}
                            </p>
                          </div>
                        )}
                    </>
                  );
                })()}
                {selectedState && (
                  <div className="mt-3 p-3 bg-muted/20 rounded-md border border-input">
                    <span className="text-xs font-medium text-text-default">
                      State:
                    </span>
                    <p className="text-sm">{selectedState}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Specialist Traits */}
          {displayTraits &&
            displayTraits.filter((trait) => trait.active).length > 0 && (
              <div className="mb-6">
                <h4 className="text-sm font-medium text-text-default mb-3">
                  Additional Population Characteristics
                </h4>
                <div className="space-y-2">
                  {displayTraits
                    .filter((trait) => trait.active)
                    .map((trait, idx) => (
                      <div
                        key={idx}
                        className="p-3 bg-muted/20 rounded-md border border-input"
                      >
                        <span className="text-xs font-medium text-text-default">
                          {trait.title}:
                        </span>
                        <p className="text-sm">{trait.values.join(", ")}</p>
                      </div>
                    ))}
                </div>
              </div>
            )}

          {/* Personas */}
          {personas && personas.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-text-default mb-3">
                Personas ({personas.length})
              </h4>
              <div className="space-y-2">
                {personas.slice(0, 3).map((persona, idx) => (
                  <div
                    key={idx}
                    className="p-3 bg-muted/20 rounded-md border border-input"
                  >
                    <p className="text-sm font-medium">{persona.name}</p>
                    <p className="text-xs text-text-light">
                      {persona.age}, {persona.gender}, {persona.occupation}
                    </p>
                  </div>
                ))}
                {personas.length > 3 && (
                  <p className="text-xs text-text-light">
                    ... and {personas.length - 3} more personas
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Show message if no WHO data */}
          {(!displayTraits ||
            displayTraits.filter((trait) => trait.active).length === 0) &&
            (!personas || personas.length === 0) && (
              <div className="p-6 text-center text-muted-foreground bg-muted/20 rounded-md border border-input">
                <p>No target population data specified.</p>
              </div>
            )}
        </div>
      </div>
      <NavigationButtons
        onBack={onBack}
        onNext={handleShowPrivacyModal}
        nextLabel="Run Concept Test"
        nextDisabled={isSubmitting}
        isSubmitting={isSubmitting}
      />

      {/* Privacy Modal */}
      <PublicExperimentQuestionModal
        showModal={showModal}
        setShowModal={setShowModal}
        onModalClose={handleModalClose}
        setExperimentPrivate={handleSetExperimentPrivate}
        setExperimentPublic={handleSetExperimentPublic}
      />
    </div>
  );
};

export default FinalReviewStep;
