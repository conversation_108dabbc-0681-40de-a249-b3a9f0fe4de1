"use client";

import { useContext, useState, useCallback } from "react";
import { useUser } from "@auth0/nextjs-auth0/client";
import { ThreeDotsBounce } from "../../../../public/icons/LoaderAnimations";
import ErrorDisplay from "./ErrorDisplay";
import ExperimentCreationContext from "./ExperimentCreationContext";
import ResearchQuestionSection from "./ResearchQuestionSection";
import AttributeList from "./_what/components/AttributeList";
import AttributeRefinement from "./_what/components/AttributeRefinement";
import NavigationButtons from "./_what/components/NavigationButtons";
import AwaitingAccessModal from "../_access/AwaitingAccessModal";
import SubscribeModal from "../_payments/SubscribeModal";
import PublicExperimentQuestionModal from "./PublicExperimentQuestionModal";
import { useAttributes } from "./_what/hooks/useAttributes";
import { useAttributeRefinement } from "./_what/hooks/useAttributeRefinement";

interface WhatComponentProps {
  onBack: () => void;
  onSubmit: () => void;
  setExperimentPrivate: () => void;
  setExperimentPublic: () => void;
  canRunExperiment: boolean;
}

// Define a modal state type for better control
type ModalState = "none" | "subscribe" | "publicQuestion" | "awaitingAccess";

const WhatComponent = ({
  onBack,
  onSubmit,
  setExperimentPrivate,
  setExperimentPublic,
  canRunExperiment,
}: WhatComponentProps) => {
  const { user } = useUser();
  const { fileState, question } = useContext(ExperimentCreationContext);

  // Custom hooks
  const {
    displayAttributes,
    isLoading,
    errorMessage,
    toggleAttribute,
    toggleLevel,
    toggleAllLevels,
    addNewAttribute,
    setIsLoading,
    isMaxLevels,
    maxAttributesReached,
    continueDisabled,
  } = useAttributes();

  const {
    isRefining,
    refinedAttributes,
    refinementError,
    fetchRefinedAttributes,
    acceptRefinedAttributes,
    cancelRefinedAttributes,
  } = useAttributeRefinement();

  // Simplified modal state management
  const [activeModal, setActiveModal] = useState<ModalState>("none");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const showRephraseBtn = fileState.file !== null;

  const handleRunExperiment = useCallback(() => {
    trackRunExperimentClicked();
    setActiveModal(canRunExperiment ? "publicQuestion" : "subscribe");
  }, [canRunExperiment]);

  const handleModalClose = () => {
    if (!isSubmitting) {
      setIsSubmitting(true);
      trackAttributesSelected();
      onSubmit();
    }
  };

  function trackAttributesSelected() {
    // @ts-ignore
    // eslint-disable-next-line no-undef
    pendo.track("attributes_selected", {});
  }

  function trackRunExperimentClicked() {
    // @ts-ignore
    // eslint-disable-next-line no-undef
    pendo.track("clicked_run_experiment", {});
  }

  return (
    <div className="flex flex-col h-full w-9/12 max-w-4xl">
      <div className="flex flex-col gap-6 items-start overflow-y-auto">
        {/* Research Question Section */}
        <ResearchQuestionSection
          question={question}
          heading="What features would you like to include in the survey?"
          subheading="Please select at least 2 attributes and at least 2 possible values for each. Click a text to edit it."
          headingId="ideation-what-guide-8"
        />

        {/* Attribute Refinement */}
        {showRephraseBtn && (
          <AttributeRefinement
            isRefining={isRefining}
            refinedAttributes={refinedAttributes}
            refinementError={refinementError}
            fetchRefinedAttributes={fetchRefinedAttributes}
            acceptRefinedAttributes={acceptRefinedAttributes}
            cancelRefinedAttributes={cancelRefinedAttributes}
          />
        )}

        {/* Loading Indicator */}
        {displayAttributes.length === 0 && (
          <div className="flex flex-row gap-4 items-center justify-center w-full">
            <div className="flex space-x-2 justify-center items-center">
              <ThreeDotsBounce />
            </div>
          </div>
        )}

        {/* Attribute List */}
        {displayAttributes.length > 0 && (
          <AttributeList
            attributes={displayAttributes}
            toggleAttribute={toggleAttribute}
            toggleLevel={toggleLevel}
            toggleAllLevels={toggleAllLevels}
            isLoading={isLoading}
            isMaxLevels={isMaxLevels}
            maxAttributesReached={maxAttributesReached}
            addNewAttribute={addNewAttribute}
            setIsLoading={setIsLoading}
          />
        )}

        {/* Max Attributes Message */}
        {displayAttributes.length >= 15 && (
          <div className="text-primary font-roboto text-base font-normal">
            You can only add a total of 15 attributes. If you want to add
            request more,{" "}
            <a href="https://discord.gg/3bgj4ZhABz" target="_blank">
              <span className="underline">please contact us.</span>
            </a>
          </div>
        )}
      </div>

      {/* Modals */}
      <PublicExperimentQuestionModal
        setExperimentPrivate={setExperimentPrivate}
        setExperimentPublic={setExperimentPublic}
        showModal={activeModal === "publicQuestion"}
        setShowModal={() => setActiveModal("none")}
        onModalClose={handleModalClose}
      />
      <AwaitingAccessModal
        showModal={activeModal === "awaitingAccess"}
        setShowModal={() => setActiveModal("none")}
      />
      <SubscribeModal
        showModal={activeModal === "subscribe"}
        setShowModal={() => setActiveModal("none")}
      />

      {/* Error Message - Fixed height container to prevent layout shifts */}
      <div className="h-[60px] sticky bottom-[60px] flex items-center w-full">
        {errorMessage && <ErrorDisplay message={errorMessage} fixed={true} />}
      </div>

      {/* Navigation Buttons */}
      <NavigationButtons
        onBack={onBack}
        onRun={handleRunExperiment}
        continueDisabled={continueDisabled()}
      />
    </div>
  );
};

export default WhatComponent;
