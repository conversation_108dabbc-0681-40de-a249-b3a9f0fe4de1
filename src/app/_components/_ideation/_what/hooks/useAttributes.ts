"use client";

import { useState, useCallback, useContext, useEffect } from "react";
import ExperimentCreationContext from "../../ExperimentCreationContext";
import { DisplayAttribute, Level } from "../../objects";
import * as Sentry from "@sentry/react";

/**
 * Custom hook for managing attributes in the WhatComponent
 */
export function useAttributes() {
  const { displayAttributes, setDisplayAttributes, question, where } =
    useContext(ExperimentCreationContext);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>("");

  // Get the number of selected attributes
  const getNumSelectedAttributes = useCallback(() => {
    return displayAttributes.filter(
      (attribute: DisplayAttribute) => attribute.active
    ).length;
  }, [displayAttributes]);

  // Check if maximum number of attributes (15) is reached
  const maxAttributesReached = useCallback(() => {
    return getNumSelectedAttributes() >= 15;
  }, [getNumSelectedAttributes]);

  /**
   * Check if maximum number of levels is reached for an attribute
   */
  const isMaxLevels = useCallback(
    (attributeIndex: number) => {
      const activeLevels = displayAttributes[attributeIndex].levels.filter(
        (level: Level) => level.active
      ).length;
      return activeLevels >= 10;
    },
    [displayAttributes]
  );

  /**
   * Toggle an attribute's active state
   */
  const toggleAttribute = useCallback(
    (index: number) => {
      // Create a new copy of the attributes array
      const newDisplayAttributes = [...displayAttributes];
      const isActivating = !newDisplayAttributes[index].active;

      // Update the attribute's active state
      newDisplayAttributes[index] = {
        ...newDisplayAttributes[index],
        active: isActivating,
      };

      // If attribute is being activated, activate all its levels
      if (isActivating) {
        newDisplayAttributes[index].levels.forEach((level) => {
          level.active = true;
        });
      }
      // If attribute is being deactivated, deactivate all its levels
      else {
        newDisplayAttributes[index].levels.forEach((level) => {
          level.active = false;
        });
      }

      // Update the attributes
      setDisplayAttributes(newDisplayAttributes);
    },
    [displayAttributes, setDisplayAttributes]
  );

  /**
   * Toggle a level's active state
   */
  const toggleLevel = useCallback(
    (attributeIndex: number, levelIndex: number) => {
      if (
        !isMaxLevels(attributeIndex) ||
        displayAttributes[attributeIndex].levels[levelIndex].active
      ) {
        const newDisplayAttributes = [...displayAttributes];
        newDisplayAttributes[attributeIndex].levels[levelIndex].active =
          !newDisplayAttributes[attributeIndex].levels[levelIndex].active;

        // If any level is active, make sure the attribute is active
        if (newDisplayAttributes[attributeIndex].levels[levelIndex].active) {
          newDisplayAttributes[attributeIndex].active = true;
        }

        setDisplayAttributes(newDisplayAttributes);
      }
    },
    [displayAttributes, setDisplayAttributes, isMaxLevels]
  );

  /**
   * Toggle all levels for an attribute
   */
  const toggleAllLevels = useCallback(
    (index: number) => {
      if (!maxAttributesReached() || displayAttributes[index].active) {
        const newDisplayAttributes = [...displayAttributes];

        newDisplayAttributes[index] = {
          ...newDisplayAttributes[index],
          active: !newDisplayAttributes[index].active,
        };

        newDisplayAttributes[index].levels.forEach((level: Level) => {
          if (!isMaxLevels(index) || !newDisplayAttributes[index].active) {
            level.active = newDisplayAttributes[index].active;
          }
        });

        setDisplayAttributes(newDisplayAttributes);
      }
    },
    [displayAttributes, setDisplayAttributes, maxAttributesReached, isMaxLevels]
  );

  /**
   * Add a new attribute
   */
  const addNewAttribute = useCallback(
    async (attributeName: string) => {
      if (!attributeName.trim()) return;

      setIsLoading(true);
      // Clear any previous error messages
      setErrorMessage("");

      // Create new attribute
      const newAttribute: DisplayAttribute = {
        attribute: attributeName,
        active: true, // Always set to active by default
        levels: [],
        attribute_type: "",
      };

      // Add to display attributes
      const updatedAttributes = [...displayAttributes, newAttribute];
      setDisplayAttributes(updatedAttributes);

      try {
        // Fetch levels for the new attribute
        const response = await fetch("/api/levels", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            why_prompt: question,
            attributes: [attributeName],
            country: where?.name,
            level_count: 4,
            existing_attributes: displayAttributes.map(
              (attr) => attr.attribute
            ),
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Check if attribute already exists
        const attrName = data[0].attribute;
        if (attributeExists(attrName)) {
          setErrorMessage(`Attribute "${attrName}" already exists.`);

          // Remove the attribute we just added
          setDisplayAttributes(displayAttributes);
        } else {
          // Update the attribute with fetched levels
          const newDisplayAttributes = [...updatedAttributes];
          const lastIndex = newDisplayAttributes.length - 1;

          newDisplayAttributes[lastIndex].attribute = attrName;

          // Add levels - keep the original logic
          const levelsArray = data[0].levels;
          levelsArray.forEach((level: string) => {
            if (level.trim() !== "") {
              newDisplayAttributes[lastIndex].levels.push({
                active: true, // Always set levels to active by default
                level: level,
              });
            }
          });

          // Ensure the attribute is active
          newDisplayAttributes[lastIndex].active = true;

          setDisplayAttributes(newDisplayAttributes);
        }
      } catch (error) {
        console.error("Error fetching levels:", error);
        Sentry.captureException(error);
        setErrorMessage("Failed to fetch levels. Please try again.");

        // Remove the attribute we just added on error
        setDisplayAttributes(displayAttributes);
      } finally {
        setIsLoading(false);
      }
    },
    [displayAttributes, question, where, setDisplayAttributes]
  );

  /**
   * Check if an attribute already exists
   */
  const attributeExists = useCallback(
    (attrName: string) => {
      return displayAttributes.some(
        (attr) => attrName.toLowerCase() === attr?.attribute.toLowerCase()
      );
    },
    [displayAttributes]
  );

  // getNumSelectedAttributes is already defined above

  /**
   * Check if continue button should be disabled and get validation error message
   * Returns an object with isDisabled and validationMessage
   */
  const validateContinueButton = useCallback(() => {
    const numSelectedAttributes = getNumSelectedAttributes();

    // Need at least 2 attributes
    if (numSelectedAttributes < 2) {
      return {
        isDisabled: true,
        validationMessage: "At least two attributes must be selected",
      };
    }

    // Maximum of 8 attributes can be selected for continuing
    // This allows users to select more than 8 for experimentation
    // but prevents continuing with more than 8
    if (numSelectedAttributes > 8) {
      return {
        isDisabled: true,
        validationMessage: "Maximum of 8 attributes can be selected",
      };
    }

    // Each selected attribute needs at least 2 levels
    for (const attr of displayAttributes) {
      if (attr.active) {
        const activeLevels = attr.levels.filter((level) => level.active).length;
        if (activeLevels < 2) {
          return {
            isDisabled: true,
            validationMessage:
              "All selected attributes must have at least two levels",
          };
        }
      }
    }

    // All validations pass
    return { isDisabled: false, validationMessage: "" };
  }, [displayAttributes, getNumSelectedAttributes]);

  // Update error message when validation changes
  useEffect(() => {
    // Don't show validation errors while loading
    if (isLoading) {
      return;
    }

    const { validationMessage } = validateContinueButton();

    // Only set error message if it's different from current one
    if (validationMessage !== errorMessage) {
      setErrorMessage(validationMessage);
    }
  }, [validateContinueButton, setErrorMessage, errorMessage, isLoading]);

  // Simplified continueDisabled function that doesn't set state
  const continueDisabled = useCallback(() => {
    // Always disable the continue button while loading
    if (isLoading) {
      return true;
    }
    return validateContinueButton().isDisabled;
  }, [validateContinueButton, isLoading]);

  return {
    // State
    displayAttributes,
    isLoading,
    errorMessage,

    // Actions
    toggleAttribute,
    toggleLevel,
    toggleAllLevels,
    addNewAttribute,
    setIsLoading,
    setErrorMessage,

    // Helpers
    isMaxLevels,
    maxAttributesReached,
    getNumSelectedAttributes,
    continueDisabled,
  };
}
