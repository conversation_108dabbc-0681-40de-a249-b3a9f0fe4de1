"use client";

import React, { useContext, useState } from "react";
import ExperimentCreationContext from "../../ExperimentCreationContext";
import Checkbox from "@/app/_components/_util/Checkbox";

interface AddNewLevelProps {
  attributeIndex: number;
  isMaxLevels: (attributeIndex: number) => boolean;
}

/**
 * Component for adding a new level to an attribute
 */
export default function AddNewLevel({
  attributeIndex,
  isMaxLevels,
}: AddNewLevelProps) {
  const { displayAttributes, setDisplayAttributes } = useContext(
    ExperimentCreationContext
  );
  const [inputValue, setInputValue] = useState("");

  // Add a new level to the attribute
  const renderNewLevel = () => {
    if (!inputValue.trim()) return;

    const newDisplayAttributes = [...displayAttributes];
    newDisplayAttributes[attributeIndex].levels.push({
      level: inputValue,
      active: false,
    });

    const levelIndex = newDisplayAttributes[attributeIndex].levels.length - 1;

    // If max has not been reached, activate level
    if (!isMaxLevels(attributeIndex)) {
      newDisplayAttributes[attributeIndex].levels[levelIndex].active = true;
    }

    setDisplayAttributes(newDisplayAttributes);
    setInputValue("");
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    renderNewLevel();
  };

  return (
    <div className="flex flex-row gap-3">
      <form className="flex flex-row gap-3 w-full" onSubmit={handleSubmit}>
        <div>
          <Checkbox
            selected={false}
            toggleSelected={() => {
              renderNewLevel();
              setInputValue("");
            }}
          />
        </div>
        <input
          className="flex-grow w-full text-text-dark text-base font-roboto font-medium bg-transparent focus:border-b focus:outline-none"
          placeholder="Add new level..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
        />
      </form>
    </div>
  );
}
