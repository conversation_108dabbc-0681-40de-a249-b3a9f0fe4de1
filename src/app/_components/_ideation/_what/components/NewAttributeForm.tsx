"use client";

import React, { useState } from "react";
import Checkbox from "@/app/_components/_util/Checkbox";

interface NewAttributeFormProps {
  addNewAttribute: (attributeName: string) => Promise<void>;
  setIsLoading?: (isLoading: boolean) => void;
  isMaxAttr: () => boolean;
}

/**
 * Component for adding a new attribute
 */
export default function NewAttributeForm({
  addNewAttribute,
  // setIsLoading is not used but kept for interface compatibility
  setIsLoading: _setIsLoading,
  // isMaxAttr is no longer used since we allow adding attributes even when max is reached
  isMaxAttr: _isMaxAttr,
}: NewAttributeFormProps) {
  const [inputValue, setInputValue] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputValue.trim()) return;

    await addNewAttribute(inputValue);
    setInputValue("");
  };

  return (
    <div className="w-full">
      <form className="w-full" onSubmit={handleSubmit}>
        <div className="flex flex-row flex-grow justify-center gap-3 items-center p-4 self-stretch w-full bg-white rounded-lg border">
          <div className="flex flex-row gap-4">
            <Checkbox selected={false} />
          </div>
          <input
            className="flex-grow text-text-dark text-base font-roboto font-medium bg-transparent focus:border-b focus:outline-none"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Type new attribute..."
          />
        </div>
      </form>
    </div>
  );
}
