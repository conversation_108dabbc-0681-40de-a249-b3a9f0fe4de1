import {
  useState,
  useEffect,
  Dispatch,
  SetStateAction,
  useContext,
  useCallback,
} from "react";
import type {
  Dispatch as ReactDispatch,
  SetStateAction as ReactSetStateAction,
} from "react";
import ExperimentCreationContext, {
  CausalQuestion,
  QueryHistory,
} from "../../../../_components/_ideation/ExperimentCreationContext";
import traitValues from "../../../../_components/_ideation/traits";
import { useSubscription } from "@/app/hooks/useSubscription";
import { getExampleQueries } from "../../data/exampleThemes";

interface UseWhyComponentProps {
  initialQuery?: string;
  setErrorMessage: Dispatch<SetStateAction<string | null>>;
  onComplete: () => void;
  setTraits: ReactDispatch<ReactSetStateAction<Record<string, any>>>;
}

export const useWhyComponent = ({
  initialQuery,
  setErrorMessage,
  onComplete,
  setTraits,
}: UseWhyComponentProps) => {
  const {
    question,
    validatedQuestions,
    setQuestion,
    setFocus,
    setDisplayAttributes,
    setValidatedQuestions,
    causalQuestions,
    setCausalQuestions,
    queryHistory,
    setQueryHistory,
    lastSubmittedQuery,
    setLastSubmittedQuery,
    showHistory,
    setShowHistory,
  } = useContext(ExperimentCreationContext);

  const [showSubscribeModal, setShowSubscribeModal] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);
  const [causalitySuggestions, setCausalitySuggestions] = useState<string[]>(
    []
  );
  const [showCausalitySuggestions, setShowCausalitySuggestions] =
    useState<boolean>(false);

  const { roles } = useSubscription();

  useEffect(() => {
    if (initialQuery) {
      setQuestion(initialQuery);

      if (getExampleQueries().includes(initialQuery.trim())) {
        const newValidatedQuestions = new Set(validatedQuestions);
        newValidatedQuestions.add(initialQuery.trim());
        setValidatedQuestions(newValidatedQuestions);
      }
    }
  }, [initialQuery, setQuestion, validatedQuestions, setValidatedQuestions]);

  // Get button text based on state
  const getButtonText = useCallback(() => {
    if (isLoading) return "Generating...";
    if (!causalQuestions.length) return "Brainstorm";
    if (question !== lastSubmittedQuery) return "Continue to brainstorm";
    return "Retry";
  }, [isLoading, causalQuestions.length, question, lastSubmittedQuery]);

  // Generate causal questions
  const handleGenerateQuestions = useCallback(async () => {
    if (!question.trim()) {
      setErrorMessage("Please enter an experiment description");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/experiments/conjoint-statements", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          why_prompt: question,
          attribute_count: 5,
          statement_history: queryHistory
            .map((q: QueryHistory) => q.causalQuestions)
            .flat()
            .map((q: CausalQuestion) => q.text),
          suggestion_count: 5,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate causal questions");
      }

      const data = await response.json();

      if (data.flag === "Error") {
        setErrorMessage(data.suggestions[0].text);
        return;
      } else {
        setErrorMessage(null);
      }

      const newQuestions = [...data.suggestions].reverse();
      setCausalQuestions(newQuestions);
      setLastSubmittedQuery(question);

      // Update context with the new questions
      const newValidatedQuestions = new Set(validatedQuestions);
      newQuestions.forEach((q: CausalQuestion) =>
        newValidatedQuestions.add(q.text)
      );
      setValidatedQuestions(newValidatedQuestions);

      // Add to history
      setQueryHistory([
        ...queryHistory,
        {
          query: question,
          causalQuestions: newQuestions,
          timestamp: Date.now(),
        },
      ]);
    } catch (error) {
      setErrorMessage("Failed to generate causal questions. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [
    question,
    queryHistory,
    setErrorMessage,
    setCausalQuestions,
    setLastSubmittedQuery,
    setValidatedQuestions,
    setQueryHistory,
    validatedQuestions,
  ]);

  // Select a question to proceed
  const handleQuestionSelect = useCallback(
    (selectedQuestion: CausalQuestion) => {
      // Make sure the selected question is validated
      if (selectedQuestion.text && selectedQuestion.text.trim()) {
        const newValidatedQuestions = new Set(validatedQuestions);
        newValidatedQuestions.add(selectedQuestion.text.trim());
        setValidatedQuestions(newValidatedQuestions);
      }

      setFocus(selectedQuestion.text);
      setDisplayAttributes([]);
      setTraits(traitValues);
      setQuestion(selectedQuestion.text);
      onComplete();
    },
    [
      setFocus,
      setDisplayAttributes,
      setTraits,
      setQuestion,
      onComplete,
      validatedQuestions,
      setValidatedQuestions,
    ]
  );

  // Load from history
  const loadFromHistory = useCallback(
    (historicalQuery: QueryHistory) => {
      setQuestion(historicalQuery.query);
      setCausalQuestions(historicalQuery.causalQuestions);
      setLastSubmittedQuery(historicalQuery.query);
    },
    [setQuestion, setCausalQuestions, setLastSubmittedQuery]
  );

  const toggleHistory = () => setShowHistory(!showHistory);

  const checkCausality = async (query: string) => {
    try {
      setShowCausalitySuggestions(false);
      setCausalitySuggestions([]);
      setErrorMessage(null);

      // If query is an example, treat as causal without API call
      if (getExampleQueries().includes(query.trim())) {
        const newValidatedQuestions = new Set(validatedQuestions);
        newValidatedQuestions.add(query.trim());
        setValidatedQuestions(newValidatedQuestions);
        return true;
      }

      const response = await fetch("/api/copilot/check-causality", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          why_prompt: query,
        }),
      });

      if (!response.ok) {
        setErrorMessage(
          "Unable to validate your question as causal. Try phrasing it as 'What factors influence...' or 'What causes...' Click on one of the rotating examples below for inspiration."
        );
        return false;
      }

      const data = await response.json();
      if (data.error) {
        setErrorMessage(data.error);
        return false;
      }

      // Process and store suggestions
      let extractedSuggestions: string[] = [];

      // Handle different response formats from the API
      if (data.suggestions && Array.isArray(data.suggestions)) {
        extractedSuggestions = data.suggestions;
      } else if (
        data.suggested_questions &&
        Array.isArray(data.suggested_questions)
      ) {
        extractedSuggestions = data.suggested_questions;
      } else if (typeof data.suggestions === "string") {
        extractedSuggestions = data.suggestions
          .split(",")
          .map((s: string) => s.trim());
      } else if (data.message && typeof data.message === "string") {
        extractedSuggestions = [data.message];
      }

      if (extractedSuggestions.length === 0) {
        extractedSuggestions = [
          "What factors influence " + query.toLowerCase().replace(/\?$/, ""),
          "What causes " + query.toLowerCase().replace(/\?$/, ""),
          "What drives " + query.toLowerCase().replace(/\?$/, ""),
        ];
      }

      extractedSuggestions = extractedSuggestions
        .filter((s) => s && s.trim().length > 0)
        .slice(0, 5);

      setCausalitySuggestions(extractedSuggestions);
      setShowCausalitySuggestions(true);

      if (data.is_causal === false) {
        setErrorMessage(
          "The statement is not causal. Please review suggestions."
        );
        return false;
      } else {
        setErrorMessage(null);
        const newValidatedQuestions = new Set(validatedQuestions);
        newValidatedQuestions.add(query);
        setValidatedQuestions(newValidatedQuestions);
      }

      return data.is_causal === true;
    } catch (error) {
      setErrorMessage(
        "Unable to validate your question as causal. Try phrasing it as 'What factors influence...' or 'What causes...' Click on one of the rotating examples below for inspiration."
      );
      return false;
    }
  };

  return {
    // State
    query: question,
    isLoading,
    // isCheckingCausality,
    causalQuestions,
    queryHistory,
    showHistory,
    showSubscribeModal,
    roles,
    causalitySuggestions,
    showCausalitySuggestions,
    validatedQuestions,

    // Actions
    setQuery: setQuestion,
    setShowSubscribeModal,
    handleGenerateQuestions,
    handleQuestionSelect,
    // handleContinueWithCurrentQuery,
    loadFromHistory,
    toggleHistory,
    // clearHistory,
    getButtonText,
    setCausalQuestions,
    setQueryHistory,
    setShowHistory,
    checkCausality,
    setCausalitySuggestions,
    setShowCausalitySuggestions,
    setValidatedQuestions,
  };
};

export default useWhyComponent;
