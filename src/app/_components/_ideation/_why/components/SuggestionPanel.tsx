import React from "react";
import { ArrowRight } from "lucide-react";
import SuggestionQuestions from "../SuggestionQuestions";
import { getExampleQueries } from "../../data/exampleThemes";

interface SuggestionPanelProps {
  showCausalitySuggestions: boolean;
  causalitySuggestions: string[];
  errorMessage: string | null;
  onSuggestionSelect: (suggestion: string) => void;
  onContinueWithCurrent: () => void;
  onCancel: () => void;
  query: string;
}

const SuggestionPanel: React.FC<SuggestionPanelProps> = ({
  showCausalitySuggestions,
  causalitySuggestions,
  errorMessage,
  onSuggestionSelect,
  onContinueWithCurrent,
  onCancel,
  query,
}) => {
  // Skip showing suggestions if this is an example query
  const isExampleQuery = getExampleQueries().includes(query.trim());

  if (
    !showCausalitySuggestions ||
    causalitySuggestions.length === 0 ||
    isExampleQuery
  ) {
    return null;
  }

  return (
    <div className="mt-6 animate-fade-text border-2 border-[#2D2E61]/20 p-4 rounded-xl bg-white/90 shadow-sm">
      <h3 className="text-lg font-semibold text-[#2D2E61] mb-4">
        Suggested Questions:
      </h3>
      <SuggestionQuestions
        suggestion={causalitySuggestions}
        onSuggestionClick={onSuggestionSelect}
      />
      <div className="mt-4 flex justify-end gap-3">
        <button
          onClick={onCancel}
          className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 flex items-center gap-2 font-medium"
        >
          Cancel
        </button>
        {!errorMessage && (
          <button
            onClick={onContinueWithCurrent}
            className="px-4 py-2 text-sm bg-[#2D2E61] text-white rounded-lg hover:bg-[#2D2E61]/90 transition-all duration-200 flex items-center gap-2 font-medium shadow-sm"
          >
            Continue with Current Query
            <ArrowRight size={14} />
          </button>
        )}
      </div>
    </div>
  );
};

export default SuggestionPanel;
