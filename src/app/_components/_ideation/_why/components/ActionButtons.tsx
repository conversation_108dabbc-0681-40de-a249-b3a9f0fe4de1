import React from "react";
import { <PERSON>, <PERSON>R<PERSON>, RefreshCw } from "lucide-react";
import { getExampleQueries } from "../../data/exampleThemes";

interface ActionButtonsProps {
  isLoading: boolean;
  isCheckingCausality: boolean;
  isAlreadyCausal: boolean;
  showCausalitySuggestions: boolean;
  errorMessage: string | null;
  query: string;
  handleGenerateQuestions: () => void;
  handleContinue: () => Promise<void>;
  getButtonText: () => string;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  isLoading,
  isCheckingCausality,
  isAlreadyCausal,
  showCausalitySuggestions,
  errorMessage,
  query,
  handleGenerateQuestions,
  handleContinue,
  getButtonText,
}) => {
  const isExampleQuery = getExampleQueries().includes(query.trim());

  const shouldShowProceed =
    isAlreadyCausal ||
    (showCausalitySuggestions && !errorMessage) ||
    isExampleQuery;

  return (
    <div className="flex gap-3">
      <button
        onClick={handleGenerateQuestions}
        disabled={isLoading}
        className="px-5 py-2.5 bg-white text-[#2D2E61] border border-[#2D2E61] rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all duration-200 font-medium"
      >
        {isLoading ? (
          <span className="flex items-center gap-2">
            <RefreshCw size={16} className="animate-spin" />
            Generating...
          </span>
        ) : (
          <>
            <Brain size={16} />
            {getButtonText()}
          </>
        )}
      </button>
      <button
        onClick={handleContinue}
        disabled={!query.trim() || isCheckingCausality}
        className="px-5 py-2.5 bg-[#2D2E61] text-white rounded-lg hover:bg-[#2D2E61]/90 disabled:bg-[#2D2E61]/50 flex items-center gap-2 transition-all duration-200 font-medium shadow-sm"
      >
        {isCheckingCausality ? (
          <span className="flex items-center gap-2">
            <RefreshCw size={16} className="animate-spin" />
            Checking...
          </span>
        ) : shouldShowProceed ? (
          <>
            Proceed
            <ArrowRight size={16} />
          </>
        ) : (
          <>
            Continue
            <ArrowRight size={16} />
          </>
        )}
      </button>
    </div>
  );
};

export default ActionButtons;
