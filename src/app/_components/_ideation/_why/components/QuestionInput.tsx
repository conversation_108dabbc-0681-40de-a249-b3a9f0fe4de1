import React from "react";

interface QuestionInputProps {
  query: string;
  setQuery: (query: string) => void;
  errorMessage: string | null;
  showCausalitySuggestions: boolean;
}

const QuestionInput: React.FC<QuestionInputProps> = ({
  query,
  setQuery,
  errorMessage,
  showCausalitySuggestions,
}) => {
  return (
    <div className="space-y-4 w-full">
      <textarea
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Describe the experiment you want to create..."
        className="flex-1 w-full h-32 p-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#2D2E61] focus:border-transparent transition-all duration-200 resize-none text-gray-800 placeholder:text-gray-400 shadow-sm"
      />

      {errorMessage && !showCausalitySuggestions && (
        <div className="text-sm text-red-500 bg-red-50 p-3 rounded-lg border border-red-100 animate-fade-text">
          <p>{errorMessage}</p>
        </div>
      )}
    </div>
  );
};

export default QuestionInput;
