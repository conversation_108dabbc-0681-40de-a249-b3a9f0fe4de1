import { useState, useEffect, useContext, useCallback } from "react";
import {
  FinalSelectedPopulationTraits,
  PopulationTraits,
  ValidationResponse,
} from "../../objects";

import { ValidationContext } from "../contexts/ValidationContext";

/**
 * Hook for handling population trait validation logic
 * Uses ValidationContext for state management when available
 *
 * @param traits - The population traits to validate
 * @param question - The research question
 * @param when - The time period
 * @param where - The location
 * @param accessToken - The access token for API calls
 * @returns Validation state and handlers
 */
export function useTraitValidation(
  getCurrentTraits: () => PopulationTraits,
  setPopulationTraits: (traits: any) => void,
  setProductExists?: (data: any) => void,
  setCurrentStep?: (step: "first" | "second" | "third") => void,
  haveTraitsChanged?: () => boolean,
  markTraitsAsValidated?: () => void
) {
  // Try to use ValidationContext if available
  const validationContext = useContext(ValidationContext);

  const [validationLoading, setValidationLoading] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Use local state with null/0 defaults
  const [validationResults, setValidationResultsState] =
    useState<ValidationResponse | null>(null);

  const [populationSizeCountState, setPopulationSizeCountState] =
    useState<number>(0);

  const [currentPopulationSize, setCurrentPopulationSize] = useState<number>(0);
  const [selectedPopulationTraitsState, setSelectedPopulationTraitsState] =
    useState<FinalSelectedPopulationTraits | null>(null);

  // Use context values if available, otherwise use local state
  const validationResultsValue =
    validationContext?.validationResults ?? validationResults;
  const setValidationResults =
    validationContext?.setValidationResults ?? setValidationResultsState;

  const populationSizeCount =
    validationContext?.populationSizeCount ?? populationSizeCountState;
  const setPopulationSizeCount =
    validationContext?.setPopulationSizeCount ?? setPopulationSizeCountState;

  const selectedPopulationTraits =
    validationContext?.selectedPopulationTraits ??
    selectedPopulationTraitsState;
  const setSelectedPopulationTraits =
    validationContext?.setSelectedPopulationTraits ??
    setSelectedPopulationTraitsState;

  // Get setSelectedOption from context if available
  const setSelectedOption = validationContext?.setSelectedOption;
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [tokenLoading, setTokenLoading] = useState(false);

  // Memoize token fetching to prevent multiple calls
  const fetchToken = useCallback(async () => {
    if (tokenLoading || accessToken) return; // Prevent duplicate calls

    setTokenLoading(true);
    try {
      const response = await fetch("/api/token");
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();
      setAccessToken(data.accessToken);
    } catch (error) {
      console.error("Failed to fetch access token:", error);
    } finally {
      setTokenLoading(false);
    }
  }, [tokenLoading, accessToken]);

  useEffect(() => {
    fetchToken();
  }, [fetchToken]);

  // Retry fetch function for API calls
  const retryFetch = async (fetchFunction: () => Promise<any>, retries = 3) => {
    let lastError: Error | null = null;

    for (let i = 0; i <= retries; i++) {
      try {
        return await fetchFunction();
      } catch (error) {
        lastError = error as Error;
        if (i === retries) {
          throw lastError;
        }
      }
    }
  };

  // Handle validation of population traits
  const handleValidation = async () => {
    // If traits haven't changed and we have existing results, return them
    if (haveTraitsChanged && !haveTraitsChanged() && validationResultsValue) {
      return validationResultsValue;
    }

    try {
      setValidationLoading(true);
      setValidationError(null);

      const currentTraits = getCurrentTraits();

      const payload = {
        age: currentTraits.age,
        education_level: currentTraits.education_level,
        gender: currentTraits.gender.map(
          (g) => g.charAt(0).toUpperCase() + g.slice(1).toLowerCase()
        ),
        household_income: currentTraits.household_income,
        number_of_children: currentTraits.number_of_children,
        number_of_records: 300,
        racial_group: currentTraits.racial_group,
        state: currentTraits.state,
      };

      const validatePopulationTraits = async () => {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/populations/validate`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },

            body: JSON.stringify(payload),
          }
        );

        if (!response.ok) {
          throw new Error(`Validation failed with status: ${response.status}`);
        }

        const data = await response.json();

        if (!data || !data.original) {
          throw new Error("Invalid response data from validation endpoint");
        }

        return data;
      };

      const data = await retryFetch(validatePopulationTraits, 2);

      // Explicitly clear any validation errors
      setValidationError(null);

      // Update validation results in context or state
      setValidationResults(data);
      setPopulationSizeCount(data.original.population_size);

      // Set the selected option to "suggestion" for new validation results
      if (setSelectedOption) {
        setSelectedOption("suggestion");
      }

      // Mark traits as validated
      if (markTraitsAsValidated) {
        markTraitsAsValidated();
      }

      // Return the validation data for the caller to use
      return data;
    } catch (error) {
      // Provide more specific error messages based on the type of error
      if (error instanceof Error) {
        if (
          error.message.includes("status: 401") ||
          error.message.includes("status: 403")
        ) {
          setValidationError(
            "Authorization error. Please try logging in again."
          );
        } else if (error.message.includes("status: 5")) {
          setValidationError(
            "Server error. Our team has been notified. Please try again later."
          );
        } else if (
          error.message.includes("Network Error") ||
          error.message.includes("Failed to fetch")
        ) {
          setValidationError(
            "Network error. Please check your connection and try again."
          );
        } else {
          setValidationError(
            "Failed to validate population traits. Please try again."
          );
        }
      } else {
        setValidationError(
          "Failed to validate population traits. Please try again."
        );
      }

      throw error; // Re-throw for caller to handle
    } finally {
      setValidationLoading(false);
    }
  };

  // Handle final trait selection
  const handleFinalTraits = (
    finalData: FinalSelectedPopulationTraits
  ): void => {
    setCurrentPopulationSize(finalData.population_size);
    setSelectedPopulationTraits(finalData);
  };

  return {
    validationLoading,
    validationError,
    setValidationError,
    validationResults: validationResultsValue, // Use the correct variable
    populationSizeCount,
    currentPopulationSize,
    selectedPopulationTraits,
    handleValidation,
    handleFinalTraits,
  };
}
