import React from "react";
import { StateData } from "../../../objects";
import RangeSelector from "./RangeSelector";
import TraitSelectionGroup from "./TraitSelectionGroup";
import LoadingIndicator from "../../../../_util/LoadingIndicator";
import {
  education,
  gender,
  children,
  race,
} from "../../../_whenwhere/PopulationTraitsData";
import { useDemographicTraits } from "../../contexts/DemographicTraitsContext";

interface DemographicTraitsFormProps {
  onContinue: () => void;
  onBack: () => void;
  stateData: StateData;
  isUSA: boolean;
  selectedState: string | null;
  validationError: string | null;
  validationLoading: boolean;
  handleValidation: () => Promise<void>;
  handleGetAgeRange: (ageRange: number[]) => void;
  handleGetIncomeRange: (incomeRange: number[]) => void;
  getEduValue: (value: string, isActive: boolean) => void;
  getGenderValue: (value: string, isActive: boolean) => void;
  getChildrenValue: (value: string, isActive: boolean) => void;
  getRaceValue: (value: string, isActive: boolean) => void;
}

/**
 * Form component for selecting demographic traits
 */
const DemographicTraitsForm: React.FC<DemographicTraitsFormProps> = ({
  // We're not using these props directly, but they're required by the interface
  // onContinue,
  // onBack,
  stateData,
  // isUSA,
  // selectedState,
  // validationError, // Not using this anymore to avoid duplicate error messages
  validationLoading,
  // handleValidation,
  handleGetAgeRange,
  handleGetIncomeRange,
  getEduValue,
  getGenderValue,
  getChildrenValue,
  getRaceValue,
}) => {
  // Get values and actions from context
  const {
    ageRange,
    incomeRange,
    educationValues,
    genderValues,
    childrenValues,
    raceValues,
    setAgeRange,
    setIncomeRange,
  } = useDemographicTraits();

  // Handle age range changes
  const handleAgeRangeChange = (newRange: number[]) => {
    // Ensure we have exactly two elements for the tuple
    const typedRange: [number, number] =
      newRange.length === 2 ? [newRange[0], newRange[1]] : [18, 65]; // Default values if invalid

    setAgeRange(typedRange);
    handleGetAgeRange(typedRange);
  };

  // Handle income range changes
  const handleIncomeRangeChange = (newRange: number[]) => {
    // Ensure we have exactly two elements for the tuple
    const typedRange: [number, number] =
      newRange.length === 2 ? [newRange[0], newRange[1]] : [0, 100000]; // Default values if invalid

    setIncomeRange(typedRange);
    handleGetIncomeRange(typedRange);
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col h-full">
        <div className="flex flex-col gap-6 items-start max-w-4xl overflow-y-auto">
          <div className="flex flex-col gap-2 items-stretch w-full">
            <h2 className="text-text-dark font-roboto font-semibold text-2xl">
              Define characteristics of the people you want to synthesize
            </h2>

            {/* Show loading indicator below the heading when validating */}
            {validationLoading && (
              <div className="w-full flex py-2 mt-2">
                <LoadingIndicator
                  message="We are validating your selected traits ... Please hold on for a moment."
                  size="md"
                />
              </div>
            )}
          </div>

          {/* Always show the form, regardless of loading state */}
          <div className="w-full pb-4 bg-white border rounded-lg p-4 items-stretch border-primary">
            <div className="w-100 p-4">
              {/* Age Range Selector */}
              <RangeSelector
                title="Age"
                range={ageRange}
                min={stateData.Age.min}
                max={stateData.Age.max}
                onChange={handleAgeRangeChange}
              />

              {/* Income Range Selector */}
              <RangeSelector
                title="Income"
                range={incomeRange}
                min={stateData["Household income"].min}
                max={stateData["Household income"]["95th_percentile"]}
                onChange={handleIncomeRangeChange}
                isCurrency={true}
              />
            </div>

            <TraitSelectionGroup
              title="Education"
              options={education}
              selectedValues={educationValues}
              onChange={getEduValue}
            />

            <TraitSelectionGroup
              title="Gender"
              options={gender}
              selectedValues={genderValues}
              onChange={getGenderValue}
            />

            <TraitSelectionGroup
              title="Number of Children"
              options={children}
              selectedValues={childrenValues}
              onChange={getChildrenValue}
            />

            <TraitSelectionGroup
              title="Racial Group"
              options={race}
              selectedValues={raceValues}
              onChange={getRaceValue}
            />
          </div>

          {/* Removed the top validation error display to avoid duplication */}
        </div>
      </div>
    </div>
  );
};

export default DemographicTraitsForm;
