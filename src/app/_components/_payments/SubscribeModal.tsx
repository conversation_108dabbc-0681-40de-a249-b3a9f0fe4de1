"use client";
import { Fragment, useEffect } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { useSubscription } from "@/app/hooks/useSubscription";

interface SubscribeModalProps {
  showModal: boolean;
  // eslint-disable-next-line no-unused-vars
  setShowModal: (showModal: boolean) => void;
}

const SubscribeModal = ({ showModal, setShowModal }: SubscribeModalProps) => {
  const { isLoading, handleCheckout } = useSubscription();

  // Ensure modal closes properly when ESC is pressed
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && showModal) {
        setShowModal(false);
      }
    };

    document.addEventListener("keydown", handleEscapeKey);
    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [showModal, setShowModal]);

  // Safe wrapper for the checkout handler
  const safeHandleCheckout = async () => {
    try {
      await handleCheckout();
    } catch (error) {
      console.error("Checkout failed:", error);
      // Could add error state handling here if needed
    }
  };

  return (
    <Transition.Root show={showModal} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={setShowModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#00000099] bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform w-[620px] overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all sm:my-8 sm:p-6">
                <div className="absolute right-0 top-0 pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-0"
                    onClick={() => setShowModal(false)}
                    aria-label="Close"
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 flex flex-col gap-4 text-center sm:mt-0 sm:text-left">
                    <Dialog.Title
                      as="h3"
                      className="text-2xl font-semibold leading-9 text-[#101828] font-roboto"
                    >
                      Upgrade your plan
                    </Dialog.Title>
                    <p className="text-lg font-normal -mt-2 text-[#101828]">
                      Please upgrade your plan to run the experiment.
                    </p>
                    <div className="flex flex-col gap-4 mt-2 justify-center items-start">
                      <div className="flex w-full items-end justify-start">
                        <p className="text-[#312E81] text-[32px] font-semibold">
                          $1,000
                        </p>
                        <p className="text-[#667085] text-lg pb-1 font-normal">
                          /month
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-col gap-3 text-lg text-text-dark font-roboto font-normal">
                      <div className="flex flex-row gap-2 items-center">
                        <div className="flex w-5 h-5 bg-primary rounded-full justify-center items-center">
                          <CheckIcon className="w-3.5 h-3.5 text-white stroke-[3px]" />
                        </div>
                        <p>Run unlimited experiments at big-data scale</p>
                      </div>
                      <div className="flex flex-row gap-2 items-center">
                        <div className="flex w-5 h-5 bg-primary rounded-full justify-center items-center">
                          <CheckIcon className="w-3.5 h-3.5 text-white stroke-[3px]" />
                        </div>
                        <p>Predict market impacts</p>
                      </div>
                      <div className="flex flex-row gap-2 items-center">
                        <div className="flex w-5 h-5 bg-primary rounded-full justify-center items-center">
                          <CheckIcon className="w-3.5 h-3.5 text-white stroke-[3px]" />
                        </div>
                        <p>Tailor unique and effective messaging</p>
                      </div>
                      <div className="flex flex-row gap-2 items-center">
                        <div className="flex w-5 h-5 bg-primary rounded-full justify-center items-center">
                          <CheckIcon className="w-3.5 h-3.5 text-white stroke-[3px]" />
                        </div>
                        <p>
                          Measure demand, market share and price elasticity with
                          human-level accuracy
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-6 w-full flex flex-row items-center">
                  <button
                    type="button"
                    className="mt-3 flex w-full justify-center rounded-md bg-[#312E81] px-3 py-3 text-sm font-semibold text-white sm:mt-0"
                    onMouseDown={handleCheckout}
                    disabled={isLoading}
                  >
                    {isLoading ? "Processing..." : "Upgrade"}
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default SubscribeModal;
