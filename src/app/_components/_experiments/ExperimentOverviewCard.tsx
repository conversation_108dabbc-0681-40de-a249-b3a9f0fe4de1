/* eslint-disable no-unused-vars */
"use client";
import { useEffect, useState, Fragment, useRef } from "react";
import React from "react";
import { useRouter } from "next/navigation";
import { doc, setDoc, arrayUnion } from "firebase/firestore";
import { db } from "@/app/lib/firebase";
import { usePendoTrack } from "@/hooks/usePendoTrack";
import html2canvas from "html2canvas";
import {
  ChevronUp,
  Sparkles,
  Calendar,
  Users,
  Copy,
  Target,
  BarChart2,
  ListTodo,
  Check,
  MessageSquare,
} from "lucide-react";
import { Run } from "./types";
import { useUser } from "@auth0/nextjs-auth0/client";
import SubscribeModal from "../_payments/SubscribeModal";
import Loading from "../_ui/Loading";
import { Dialog, Transition } from "@headlessui/react";
import IllusionLaborCard from "./IllusionLaborCard";
import OverviewCard from "../_util/OverviewCard";
import * as Sentry from "@sentry/react";
import { ErrorMessage } from "@/app/utils/errorMessage";
import AttributeImportanceList from "./FeatureImportanceChart";
import AmceChart from "../_util/AmceChart";
import CausalGraph from "../_graph/CausalGraph";
import { LinkType } from "../_insights/types";
import { Spinner } from "@/app/primitives/Spinner/Spinner";
import { Badge } from "@/components/ui/badge";
import { useSubscription } from "@/app/hooks/useSubscription";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import DiscreteChoiceModelTooltip from "./DiscreteChoiceModelTooltip";

interface ExperimentOverviewCardProps {
  run: Run;
  runCount: number;
  onPrivacyToggle: (id: string, isPrivate: boolean) => void; // Add this prop
  quickRefresh: () => Promise<void>;
}

type amceHBObj = {
  attribute_text: string;
  formatted_part_worth: string;
  level_text: string;
  level_text1: string;
  part_worth: number;
  row: number;
  error: number;
  desc: string;
  mindset: string;
  lowerBound: number;
  upperBound: number;
  std_error: number;
};

const fetcher = async (uri: string) => {
  const startTime = Date.now();
  try {
    const response = await fetch(uri, {
      method: "GET",
      headers: { "Content-Type": "application/json" },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    const duration = Date.now() - startTime;

    // Log error with contextual information
    Sentry.captureException(error, {
      tags: {
        api_endpoint: uri,
        status: "error",
      },
      extra: {
        response_time: `${duration}ms`,
        response_status: (error as any).response
          ? (error as any).response.status
          : "unknown",
        error_message: (error as any).message,
      },
    });

    throw error;
  }
};

interface AttributeImportance {
  attribute_text: string;
  importance: number;
}

interface CausalAttributeLevel {
  id: string;
  value: number;
  p_value: number;
  std_error: number;
}

interface CausalAttribute {
  name: string;
  levels: CausalAttributeLevel[];
}

interface CausalData {
  id: string;
  type: string;
  attributes: CausalAttribute[];
}

const buttonStyles =
  "flex bg-primary w-50 hover:bg-[#504D9A] text-white font-inter font-medium text-lg px-4 py-2 mx-2 rounded-xl items-center justify-center";

const ExperimentOverviewCard = ({
  run,
  runCount,
  onPrivacyToggle,
  quickRefresh,
}: ExperimentOverviewCardProps) => {
  const { user } = useUser();
  const { subscriptionStatus, roles } = useSubscription();
  const [collapsed, setCollapsed] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const router = useRouter();
  const [expDetail, setExpDetail] = useState<any>([]);
  const [replicateData, setReplicateData] = useState<any>({});
  const [isToggled, setIsToggled] = useState(run.is_private);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [amceData, setAmceData] = useState<any>({});
  const [chartTitle, setChartTitle] = useState<string | null>(null);

  const [featureImportanceData, setFeatureImportanceData] = useState<
    AttributeImportance[]
  >([]);
  const [causalData, setCausalData] = useState<CausalData | null>(null);
  const [specs, setSpecs] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [chartError, setChartError] = useState<string | null>(null);
  const [links, setLinks] = useState<LinkType>();
  const [hasFetchedData, setHasFetchedData] = useState(false);
  //subscription check logic

  const [showSubscribeModal, setShowSubscribeModal] = useState<boolean>(false);

  const [timer, setTimer] = useState<number>(0); // Time elapsed in seconds
  const [isTimerActive, setIsTimerActive] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  // eslint-disable-next-line no-undef
  const timerRef = useRef<{
    startTimestamp: number;
    interval: any | null;
    lastKnownElapsed: number;
  }>({
    startTimestamp: 0,
    interval: null,
    lastKnownElapsed: 0,
  });

  // Add state for tracking collaboratory creation
  const [isCreatingCollab, setIsCreatingCollab] = useState<boolean>(false);
  const [showCollabButton, setShowCollabButton] = useState<boolean>(true);
  const runName = run.name;
  const { trackFeatureUsage } = usePendoTrack();

  // Add states for tracking copy confirmation animation
  const [copyingTab1, setCopyingTab1] = useState(false);
  const [copyingTab2, setCopyingTab2] = useState(false);
  const [copyingTab3, setCopyingTab3] = useState(false);
  const isShortQuestion = run.question?.length < 120;

  useEffect(() => {
    if (typeof window !== "undefined") {
      const hostname = window.location.hostname;
      const isRestrictedDomain =
        hostname === "app.test.subconscious.ai" ||
        hostname === "app.subconscious.ai";
      setShowCollabButton(!isRestrictedDomain);
    }
  }, []);

  useEffect(() => {
    if (run.state === "running") {
      const createdAtTimestamp = new Date(run.created_at).getTime();

      // Calculate initial elapsed time
      const initialElapsed = Math.max(
        0,
        Math.floor((Date.now() - createdAtTimestamp) / 1000)
      );
      setTimer(initialElapsed);

      // Store timer state in ref for persistence across renders
      timerRef.current = {
        startTimestamp: createdAtTimestamp,
        interval: null,
        lastKnownElapsed: initialElapsed,
      };

      // Timer update function - recalculates from the origin timestamp
      const updateTimer = () => {
        const now = Date.now();
        const elapsed = Math.max(
          0,
          Math.floor((now - timerRef.current.startTimestamp) / 1000)
        );
        timerRef.current.lastKnownElapsed = elapsed;
        setTimer(elapsed);
      };

      // Handle visibility change events
      const handleVisibilityChange = () => {
        if (document.visibilityState === "visible") {
          // Clear any existing interval to prevent multiple timers
          if (timerRef.current.interval) {
            clearInterval(timerRef.current.interval);
          }

          // Immediately update timer
          updateTimer();

          // Restart interval for active tab
          timerRef.current.interval = setInterval(updateTimer, 1000);
        } else {
          // Tab is hidden, clear interval to save resources
          if (timerRef.current.interval) {
            clearInterval(timerRef.current.interval);
            timerRef.current.interval = null;
          }
        }
      };

      // Start the timer immediately and handle current visibility state
      updateTimer();
      if (document.visibilityState === "visible") {
        timerRef.current.interval = setInterval(updateTimer, 1000);
      }

      // Add event listeners for visibility and focus events
      document.addEventListener("visibilitychange", handleVisibilityChange);
      window.addEventListener("focus", updateTimer);

      // Additional event for mobile devices and potential sleep/wake cycles
      window.addEventListener("pageshow", updateTimer);

      setIsTimerActive(true);

      return () => {
        setIsTimerActive(false);
        document.removeEventListener(
          "visibilitychange",
          handleVisibilityChange
        );
        window.removeEventListener("focus", updateTimer);
        window.removeEventListener("pageshow", updateTimer);

        if (timerRef.current.interval) {
          clearInterval(timerRef.current.interval);
          timerRef.current.interval = null;
        }
      };
    }

    return () => {
      setIsTimerActive(false);
      if (timerRef.current.interval) {
        clearInterval(timerRef.current.interval);
        timerRef.current.interval = null;
      }
    };
  }, [run.state, run.created_at]);

  const handleToggleChange = async () => {
    if (
      subscriptionStatus === "Active" ||
      roles.includes("employee") ||
      roles.includes("customer") ||
      runCount <= 2
    ) {
      const newToggledState = !isToggled;
      setIsToggled(newToggledState); // Immediately update the toggle state

      try {
        const updatedPrivacyStatus = await updatePrivacyStatus(
          run.id,
          newToggledState
        );

        setIsToggled(updatedPrivacyStatus);
        onPrivacyToggle(run.id, updatedPrivacyStatus); // Call the parent callback
      } catch (error) {
        console.error("Error updating privacy status:", error);
        setIsToggled(!newToggledState); // Revert toggle state if update fails
        Sentry.captureException(error);
      }
    } else {
      setShowSubscribeModal(true);
    }
  };

  const updatePrivacyStatus = async (id: string, newPrivacyStatus: boolean) => {
    try {
      const response = await fetch(`/api/runs/${id}/update_config`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          config_update: {
            set_privacy: newPrivacyStatus,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(ErrorMessage.privacyStatus);
      }

      const data = await response.json();

      return data.configs.experiment_design.is_private;
    } catch (error) {
      console.error("Error updating privacy status:", error);
      Sentry.captureException(error);
      throw error;
    }
  };

  useEffect(() => {
    if (run.state === "finished" && run.id && !collapsed) {
      fetcher(`/api/runs/${run.id}`)
        .then((res) => {
          const {
            experimentor_why_question_prompt,
            survey_year,
            survey_country,
            why_prompt,
            year,
            country,
            pre_cooked_attributes_and_levels_lookup,
            state,
          } = res.run_details.configs.experiment_design;
          const { wandb_id } = res.run_details.configs;

          const whyPrompt = why_prompt ?? experimentor_why_question_prompt;
          const surveyYear = survey_year ?? year;
          const surveyCountry = survey_country ?? country;

          const configuredAttrLvls: any = [];
          if (pre_cooked_attributes_and_levels_lookup) {
            for (
              let i = 0;
              i < pre_cooked_attributes_and_levels_lookup.length;
              i++
            ) {
              const attrLevels: any = {};
              const currentAttribute: any =
                pre_cooked_attributes_and_levels_lookup[i][0];
              const currentLevels =
                pre_cooked_attributes_and_levels_lookup[i][1].slice(1);
              attrLevels["attribute"] = currentAttribute;
              attrLevels["levels"] = currentLevels;
              configuredAttrLvls.push(attrLevels);
            }
          }
          const data: any = {
            experiment_id: wandb_id,
            causal_question: whyPrompt,
            when: surveyYear,
            where: surveyCountry,
            state,
          };

          setReplicateData(data);
        })
        .catch((error: any) => {
          Sentry.captureException(error);
        });
    }
  }, [run.id, run.state, collapsed]);

  const copyTabAsImage = async (
    tabId: string,
    setCopying: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    const tabElement = document.getElementById(tabId);
    if (!tabElement) {
      console.error("Tab content not found");
      return;
    }

    try {
      setCopying(true);
      const canvas = await html2canvas(tabElement, {
        ignoreElements: (element) =>
          element.classList.contains("exclude-from-screenshot"),
      });
      canvas.toBlob((blob) => {
        if (blob) {
          const item = new ClipboardItem({ "image/png": blob });
          navigator.clipboard.write([item]);
          setTimeout(() => setCopying(false), 1000);
        }
      });
    } catch (error) {
      console.error("Failed to copy image:", error);
      setCopying(false);
    }
  };

  const fetchAmceCausalFeatureImportanceData = async () => {
    if (!runName) {
      console.error("runName is not defined");
      return;
    }
    setIsLoading(true);
    setChartError("");
    try {
      const artifactName = `experiment_beta_amce_${runName}`;
      const res = await fetch("/api/runs/artifact", {
        method: "POST",
        headers: { Content_Type: "application/json" },
        body: JSON.stringify({ artifactName }),
      });
      const data = await res.json();
      const amceData = data.data.AMCE_Results.AMCE_Results;
      const ChartTitle = data.data.AMCE_Results.AMCE_Title;

      const filteredAmceData = configureAmceData(amceData, "Experiment Data");
      setAmceData(filteredAmceData);

      const FeatureImportanceData = data.data.AMCE_Results.Attribute_Importance;

      if (
        FeatureImportanceData !== null &&
        FeatureImportanceData !== undefined
      ) {
        setFeatureImportanceData(FeatureImportanceData);
      }

      const transformed: CausalData = {
        id: "center_node",
        type: "center",
        attributes: [] as CausalAttribute[],
      };

      const causalAttributes: { [key: string]: CausalAttributeLevel[] } = {};

      filteredAmceData.forEach((item: any) => {
        const configuredItem = item.attribute_text;
        if (!causalAttributes[configuredItem]) {
          causalAttributes[configuredItem] = [];
        }
        causalAttributes[configuredItem].push({
          id: item.level_text,
          value: item.part_worth,
          p_value: item.std_error != 0 ? item.part_worth / item.std_error : 0.0,
          std_error: item.std_error,
        });
      });
      Object.keys(causalAttributes).forEach((attrName) => {
        const levels = causalAttributes[attrName];
        transformed.attributes.push({
          name: attrName,
          levels: levels,
        });
      });
      setCausalData(transformed);
      setChartTitle(ChartTitle[0]);
    } catch (error) {
      Sentry.captureException(error);
      setChartError("Failed to fetch data");
      console.error("Failed to fetch data:", error);
    } finally {
      setIsLoading(false);
      setHasFetchedData(true);
    }
  };

  const fetchLinks = async () => {
    if (!runName) {
      console.error("runName is not defined");
      return;
    }
    try {
      const artifactName = `langsmith_sharelinks_${runName}`;
      const res = await fetch("/api/runs/artifact", {
        method: "POST",
        headers: { Content_Type: "application/json" },
        body: JSON.stringify({ artifactName }),
      });
      const data = await res.json();

      if (data.error) {
        setLinks({ error: "LLM Traces not found for this experiment." });
        console.error("Error fetching links:", data.error);
      } else if (!data.data || Object.keys(data.data).length === 0) {
        setLinks({ error: "LLM Traces not found for this experiment." });
      } else {
        setLinks(data);
      }
    } catch (error) {
      setLinks({ error: "LLM Traces not found for this experiment." });
      console.error("Failed to fetch links:", error);
    }
  };

  useEffect(() => {
    if (!collapsed && runName) {
      fetchAmceCausalFeatureImportanceData();
      fetchLinks();
    } else if (!hasFetchedData) {
      setIsLoading(true);
    }
  }, [runName, collapsed, hasFetchedData]);

  useEffect(() => {
    if (amceData) {
      const dataArray = Array.isArray(amceData) ? amceData : [];

      const configureSpec = {
        $schema: "https://vega.github.io/schema/vega-lite/v5.json",
        background: "white",
        autosize: {
          type: "fit",
          contains: "padding",
        },
        width: "container",
        height: "container",
        padding: 5,
        title: {
          anchor: "middle",
          align: "center",
          fontSize: 18,
          fontWeight: "bold",
          orient: "top",
          subtitle: ["▲ Most Preferred    ▼ Least Preferred    ● Neutral", " "],
          subtitleColor: "#101826",
          subtitleFont: "Roboto",
          subtitleFontSize: 13,
          subtitlePadding: 15,
          text: [chartTitle || "Feature Level Effect Size"],
        },
        spacing: 10,
        facet: {
          row: {
            field: "attribute_text",
            type: "nominal",
            sort: { field: "row" },
            header: {
              title: null,
              labelAlign: "center",
              labelOrient: "top",
              labelPadding: 5,
              labelFont: "Roboto",
              labelFontSize: 15,
              labelFontWeight: "bold",
            },
          },
        },
        data: {
          values: dataArray,
        },

        spec: {
          width: 400,
          layer: [
            // Confidence interval (rule)
            {
              mark: { type: "rule", strokeWidth: 2, opacity: 1 },
              encoding: {
                x: { field: "lowerBound", type: "quantitative" },
                x2: { field: "upperBound" },
                y: {
                  field: "level_text",
                  type: "ordinal",
                  sort: { field: "row" },
                },
                color: { field: "color", scale: null },
              },
            },
            // Point markers
            {
              mark: { type: "point", filled: true, opacity: 1 },
              encoding: {
                x: {
                  field: "part_worth",
                  type: "quantitative",
                  title: "Scaled Effect Size",
                  scale: {
                    domain: [-1, 1],
                    nice: false,
                  },
                  axis: {
                    labelFontSize: 15,
                    titleFontSize: 15,
                    tickCount: 5,
                  },
                },
                y: {
                  field: "level_text",
                  type: "ordinal",
                  sort: { field: "row" },
                  axis: { title: null, labelFontSize: 15, labelLimit: 1000 },
                },
                color: {
                  field: "color",
                  type: "nominal",
                  scale: {
                    domain: ["#DC3545", "#b4b4b4", "#0d6efd"],
                    range: ["#DC3545", "#b4b4b4", "#0d6efd"],
                  },
                  legend: {
                    title: null,
                    orient: "right",
                    symbolType: "circle",
                    labelExpr:
                      "if(datum.value=='#0d6efd','Positive Effect (Reliable)', if(datum.value=='#DC3545','Negative Effect (Reliable)','Negative or Positive Effect (Unreliable)'))",
                  },
                },
                shape: {
                  field: "shape",
                  type: "nominal",
                  scale: {
                    domain: ["triangle-up", "triangle-down", "circle"],
                    range: ["triangle-up", "triangle-down", "circle"],
                  },
                  legend: null,
                },
                size: {
                  condition: [{ test: "datum.shape !== 'circle'", value: 275 }],
                  value: 70,
                },
                tooltip: [
                  { field: "attribute_text", title: "Attribute Text" },
                  { field: "level_text", title: "Level Text" },
                  {
                    field: "part_worth",
                    title: "Level Importance",
                    format: ".2f",
                  },
                  { field: "effect", title: "Effect" },
                  { field: "lowerBound", title: "Lower Bound", format: ".2f" },
                  { field: "upperBound", title: "Upper Bound", format: ".2f" },
                ],
              },
            },
            // Zero line
            {
              mark: { type: "rule", color: "#101826", strokeDash: [8, 8] },
              encoding: { x: { datum: 0 } },
            },
          ],
        },
        resolve: { scale: { y: "independent", x: "shared" } },
        config: {
          axis: {
            domainOpacity: 0,
            labelColor: "#101826",
            labelFont: "Roboto",
            labelFontSize: 15,
            titleColor: "#101826",
            titleFont: "Roboto",
            titleFontSize: 14,
          },
          legend: {
            columns: 1,
            gridAlign: "each",
            labelColor: "#101826",
            labelFont: "Roboto",
            labelFontSize: 14,
            labelLimit: 1000,
            orient: "right",
            titleFont: "Roboto",
          },
        },
      };

      setSpecs(configureSpec);
      setIsLoading(false);
    }
  }, [amceData]);

  const configureAmceData = (data: any, dataTitle: string) => {
    let configuredData: any[] = [];
    const attributeMap = new Map<
      string,
      { maxAmce: number; minAmce: number }
    >();

    // First pass: Find max and min AMCE per attribute
    data.forEach((item: any) => {
      const attr = item.attribute_text;
      if (!attributeMap.has(attr)) {
        attributeMap.set(attr, { maxAmce: -Infinity, minAmce: Infinity });
      }
      const current = attributeMap.get(attr)!;
      const amce = parseFloat(item.AMCE);
      if (amce > current.maxAmce) current.maxAmce = amce;
      if (amce < current.minAmce) current.minAmce = amce;
      attributeMap.set(attr, current);
    });

    // Second pass: Configure data points
    data.forEach((item: any) => {
      const { attribute_text, level_ids, level_text, AMCE, std_error } = item;
      const part_worth = parseFloat(AMCE);
      const lowerBound = parseFloat((part_worth - 1.96 * std_error).toFixed(4));
      const upperBound = parseFloat((part_worth + 1.96 * std_error).toFixed(4));

      const attrInfo = attributeMap.get(attribute_text)!;
      const isMax = part_worth === attrInfo.maxAmce;
      const isMin = part_worth === attrInfo.minAmce;
      const ciContainsZero = lowerBound <= 0 && upperBound >= 0;
      const isPositive = part_worth > 0;

      // Determine color and shape
      let color = "#b4b4b4";
      let shape = "circle";
      let importance_text = "Neutral";

      if (isMax) {
        shape = "triangle-up";
        importance_text = "Most preferred";
        color = lowerBound > 0 && upperBound > 0 ? "#0d6efd" : "#b4b4b4";
      } else if (isMin) {
        shape = "triangle-down";
        importance_text = "Least preferred";
        color = lowerBound < 0 && upperBound < 0 ? "#DC3545" : "#b4b4b4";
      } else {
        if (isPositive) {
          color = ciContainsZero ? "#b4b4b4" : "#0d6efd"; // Non-extreme positive
        } else {
          color = ciContainsZero ? "#b4b4b4" : "#DC3545"; // Non-extreme negative
        }
        shape = "circle";
      }
      if (attribute_text !== "Intercept") {
        configuredData.push({
          attribute_text,
          level_text,
          part_worth,
          lowerBound: Math.max(-1, lowerBound),
          upperBound: Math.min(1, upperBound),
          color,
          effect:
            part_worth > 0
              ? "Positive"
              : part_worth < 0
                ? "Negative"
                : "No Effect",
          shape,
          importance_text,
          row: parseInt(level_ids),
        });
      }
    });

    return configuredData;
  };

  const formatTime = (seconds: number) => {
    // Ensure seconds is non-negative
    seconds = Math.max(0, seconds);
    const m = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const s = (seconds % 60).toString().padStart(2, "0");
    return `${m}:${s}`;
  };

  const handleCardClick = () => {
    if (run.state === "failed") {
      return;
    }
    if (run.state === "running") {
      setShowModal(true);
    } else {
      setCollapsed(!collapsed);
      setShowModal(false);
    }
  };

  const toggleButtonStyles =
    "relative inline-block w-10 align-middle select-none transition duration-200 ease-in `${isChecked ? 'right-0.5' : ''}` ";

  useEffect(() => {
    if (Object.keys(replicateData).length > 0) {
      const experimentDesign = [
        {
          name: "Experiment ID",
          value: replicateData.experiment_id ? replicateData.experiment_id : "",
        },
        {
          name: "Location and time",
          value:
            replicateData.when && replicateData.where
              ? `${replicateData.where} ${replicateData.when}`
              : "",
        },
        {
          name: "State",
          value: Array.isArray(replicateData.state)
            ? replicateData.state.join(", ")
            : replicateData.state || "",
        },
      ];
      setExpDetail(experimentDesign);
    }
  }, [replicateData]);

  const tabDetails = [
    // Always show these tabs
    {
      tabContentName: "Feature_Importance",
      tabTitle: "Feature Importance",
      contentDetails: (
        <div id="feature-importance-tab">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={() =>
                    copyTabAsImage("feature-importance-tab", setCopyingTab2)
                  }
                  className="exclude-from-screenshot flex cursor-pointer mr-2 md:ml-2 h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-xl border-card-border bg-white hover:bg-secondary-grey shadow-sm"
                >
                  {copyingTab2 ? <Check /> : <Copy />}
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-white">Copy Image</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          {isLoading ? (
            <Loading />
          ) : featureImportanceData && featureImportanceData.length > 0 ? (
            <AttributeImportanceList
              attributeImportance={featureImportanceData}
            />
          ) : (
            chartError && (
              <div className="flex flex-col gap-2 pl-2">
                <p className="text-text-dark font-inter text-sm font-normal">
                  No data available
                </p>
              </div>
            )
          )}
        </div>
      ),
    },
    {
      tabContentName: "Feature_Level_Importance",
      tabTitle: "Feature Level Importance",
      contentDetails: (
        <div id="feature-level-importance-tab">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={() =>
                    copyTabAsImage(
                      "feature-level-importance-tab",
                      setCopyingTab1
                    )
                  }
                  className="exclude-from-screenshot flex cursor-pointer mr-2 md:ml-2 h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-xl border-card-border bg-white hover:bg-secondary-grey shadow-sm"
                >
                  {copyingTab1 ? <Check /> : <Copy />}
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-white">Copy Image</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          {isLoading ? (
            <Loading />
          ) : amceData && amceData?.length > 0 ? (
            <div className="flex justify-center items-center overflow-x-auto ">
              <AmceChart spec={specs} />
            </div>
          ) : (
            chartError && (
              <div className="flex flex-col gap-2 pl-2">
                <p className="text-text-dark font-inter text-sm font-normal">
                  No data available
                </p>
              </div>
            )
          )}
        </div>
      ),
    },

    // Conditionally include these tabs based on hostname
    ...(showCollabButton
      ? [
          {
            tabContentName: "causal_graph",
            tabTitle: "Causal Graph",
            contentDetails: (
              <div id="causal-graph-tab">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() =>
                          copyTabAsImage("causal-graph-tab", setCopyingTab3)
                        }
                        className="exclude-from-screenshot flex cursor-pointer mr-2 md:ml-2 h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-xl border-card-border bg-white hover:bg-secondary-grey shadow-sm"
                      >
                        {copyingTab3 ? <Check /> : <Copy />}
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-white">Copy Image</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                {isLoading ? (
                  <Loading />
                ) : causalData && causalData?.attributes?.length > 0 ? (
                  <div className="relative border-4 border-white-500 w-full">
                    <CausalGraph
                      graphWidth="100%"
                      graphHeight="100vh"
                      data={causalData}
                    />
                  </div>
                ) : (
                  chartError && (
                    <div className="flex flex-col gap-2 pl-2">
                      <p className="text-text-dark font-inter text-sm font-normal">
                        No data available
                      </p>
                    </div>
                  )
                )}
              </div>
            ),
          },
          {
            tabContentName: "LLM_traces",
            tabTitle: "LLM Traces",
            contentDetails: links ? (
              links.error ? (
                <div className="flex flex-col gap-2 pl-2">
                  <p className="text-text-dark font-inter text-sm font-normal">
                    {links.error}
                  </p>
                </div>
              ) : (
                <div className="flex flex-wrap justify-center">
                  {Object.entries(links.data || {}).map(([key, url]) => (
                    <div
                      key={key}
                      className="w-full md:w-1/2 xl:w-1/3 p-4 bg-white rounded shadow-md"
                    >
                      <a
                        href={url as string}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-lg text-gray-700 mb-2 hover:text-blue-600"
                      >
                        {url}
                      </a>
                    </div>
                  ))}
                </div>
              )
            ) : (
              <Loading />
            ),
          },
        ]
      : []),

    // Always show experiment details tab
    {
      tabContentName: "experiment_details",
      tabTitle: "Experiment Details",
      contentDetails: isLoading ? (
        <Loading />
      ) : expDetail && expDetail.length > 0 ? (
        <div className="flex flex-col gap-2 pl-2">
          {expDetail.map(
            (item: any) =>
              item.value !== "" && (
                <div className="flex flex-col gap-2" key={item.name}>
                  <h2 className="text-subtitle">{item.name} </h2>
                  <p>{item.value}</p>
                </div>
              )
          )}
        </div>
      ) : (
        <div className="flex flex-col gap-2 pl-2">
          <p className="text-text-dark font-inter text-sm font-normal">
            No data available
          </p>
        </div>
      ),
    },

    // AI Chat tab
    {
      tabContentName: "ai_chat",
      tabTitle: "AI Chat",
      contentDetails: (
        <div className="flex flex-col items-center justify-center py-8 px-4">
          <div className="text-center space-y-4">
            <MessageSquare className="h-12 w-12 text-primary mx-auto" />
            <h3 className="text-lg font-semibold text-gray-900">
              AI Experiment Analysis
            </h3>
            <p className="text-gray-600 max-w-md">
              Get instant AI-powered insights about your experiment results. Ask
              specific questions or start with a comprehensive overview.
            </p>
            <a
              href={`/chat/${run.id}?title=${encodeURIComponent(run.question || "Untitled Experiment")}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 bg-primary hover:bg-primary-dark text-white font-medium px-6 py-3 rounded-xl transition-colors"
            >
              <MessageSquare className="h-4 w-4" />
              <span>Open AI Chat</span>
            </a>
          </div>
        </div>
      ),
    },
  ];

  const cardDetailTop = (
    <>
      <div
        className="flex flex-row gap-10 justify-between items-center hover:cursor-pointer"
        onMouseDown={handleCardClick}
      >
        <div className="flex flex-col w-full">
          <div className="flex items-center gap-2">
            {run.state === "running" && (
              <>
                <Badge className="bg-blue-50 text-blue-700 hover:bg-blue-100 border border-blue-200 text-sm px-3 py-1 rounded-md">
                  <div className="flex items-center gap-2">
                    <Spinner className="text-blue-700" size={20} />
                    <span>Time Elapsed: {formatTime(timer)}</span>
                  </div>
                </Badge>
              </>
            )}
          </div>
          <p
            className={`text-primary-dark font-roboto font-medium text-lg ${
              isShortQuestion
                ? "" // Centered for short questions
                : "text-justify" // Justified for longer ones
            }`}
          >
            {run.question}
          </p>
        </div>

        <div className="w-fit">
          {run.state === "running" ? (
            <div className="flex gap-2">
              <Badge className="bg-green-50 text-green-700 hover:bg-green-100 border border-green-200 text-sm px-3 py-1 rounded-md">
                Running
              </Badge>
            </div>
          ) : run.state === "failed" ? (
            <div className="flex gap-2">
              <Badge className="bg-red-50 text-red-700 hover:bg-red-100 border border-red-200 text-sm px-3 py-1 rounded-md">
                Failed
              </Badge>
            </div>
          ) : (
            <ChevronUp
              id="experiments-guide-2"
              className={`h-6 w-6 text-chevron transition-all duration-300 ${
                collapsed ? "rotate-180" : "rotate-0"
              }`}
            />
          )}
        </div>
      </div>
      <Transition.Root show={showModal} as={Fragment}>
        <Dialog
          as="div"
          static
          className="fixed z-10 inset-0 overflow-y-auto"
          onClose={() => setShowModal(false)}
        >
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <Dialog.Overlay className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            </Transition.Child>

            <span
              className="hidden sm:inline-block sm:align-middle sm:h-screen"
              aria-hidden="true"
            >
              &#8203;
            </span>

            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <IllusionLaborCard />
              </div>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      <div className="flex flex-row gap-10">
        <div className="flex items-center gap-2">
          <label
            className={`${toggleButtonStyles} relative inline-flex items-center cursor-pointer`}
          >
            <input
              type="checkbox"
              id="togBtn"
              className="toggle-checkbox absolute block w-5 h-5 rounded-full appearance-none cursor-pointer transition-transform duration-200 ease-in"
              style={{
                top: "0.1rem",
                left: "0.1rem",
                transform: isToggled
                  ? "translateX(calc(100% - 0.25em))"
                  : "translateX(0)",
                backgroundColor: isToggled ? "white" : "white",
                transition:
                  "transform 0.2s ease-in-out, background-color 0.2s ease-in-out",
              }}
              checked={isToggled}
              onChange={handleToggleChange}
            />
            <span
              className="toggle-label block h-6 w-full rounded-full"
              style={{
                backgroundColor: isToggled ? "#1C1D47" : "gray",
                transition: "background-color 0.2s ease-in-out",
              }}
            ></span>
          </label>
          <p className="text-text-dark font-inter text-sm font-normal">
            Private
          </p>
        </div>

        <div className="flex flex-row gap-2">
          <Calendar />
          <p className="text-text-dark font-inter text-sm font-normal">
            {new Date(run.created_at).toLocaleDateString("default", {
              month: "long",
              day: "numeric",
              year: "numeric",
            })}
          </p>
        </div>

        <div className="flex flex-row gap-2">
          <Users />
          <p className="text-text-dark font-inter text-sm font-normal">
            Respondents: {`${run.sample_size ?? 0}` || "Pending..."}
          </p>
        </div>

        <div className="flex flex-row gap-2">
          <ListTodo />
          <p className="text-text-dark font-inter text-sm font-normal">
            Tasks per respondent:{" "}
            {`${run.tasks_per_respondent ?? 0}` || "Pending..."}
          </p>
        </div>
        <div className="flex flex-row gap-2">
          <BarChart2 />
          <p className="text-text-dark font-inter text-sm font-normal">
            Sample size: {`${run.total_number_of_tasks ?? 0}` || "Pending..."}
          </p>
        </div>
        <div className="flex flex-row gap-2">
          <Target />
          <p className="text-text-dark font-inter text-sm font-normal">
            Variance explained: {`${run.r_squared ?? 0}%` || "Pending..."}
            <DiscreteChoiceModelTooltip />
          </p>
        </div>
      </div>
    </>
  );

  const customActionDetails = [
    ...(showCollabButton
      ? [
          {
            customButton: (
              <div
                className="flex cursor-pointer mr-2 md:ml-2 h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-xl border-card-border bg-white hover:bg-secondary-grey shadow-sm"
                onClick={async () => {
                  try {
                    // Track when a user clicks the Collab button
                    trackFeatureUsage("Collab", "clicked", {
                      action: "create_collab",
                      experiment_id: run.id,
                      experiment_name: run.question || "Untitled Experiment",
                    });

                    // Set creating state to true to show spinner
                    setIsCreatingCollab(true);

                    // Fetch artifact data
                    const artifactName = `experiment_beta_amce_${runName}`;

                    const res = await fetch("/api/runs/artifact", {
                      method: "POST",
                      headers: { Content_Type: "application/json" },
                      body: JSON.stringify({ artifactName }),
                    });
                    const data = await res.json();

                    if (!res.ok) {
                      throw new Error(
                        `Failed to fetch artifact: ${res.statusText}`
                      );
                    }

                    // Store relevant artifact data
                    const artifactDetails = data.data?.AMCE_Results;

                    if (!artifactDetails) {
                      throw new Error("Artifact details are undefined");
                    }

                    const experimentsRef = doc(db, "collaboratories", run.id); // Firestore document reference

                    // Add or update experiment in Firestore with artifact details
                    await setDoc(
                      experimentsRef,
                      {
                        id: run.id,
                        name: run.question || "Untitled Experiment",
                        runName: runName,
                        createdBy: user?.email,
                        collaborators: arrayUnion({
                          email: user?.email,
                          nickname:
                            user?.nickname || user?.email?.split("@")[0],
                          role: "Owner",
                        }),
                        artifactDetails, // Store artifact data
                        createdAt: Date.now(),
                      },
                      { merge: true } // Ensures no overwriting of existing data
                    );

                    // Open the Collaboratory page in a new tab
                    window.open(`/collaboratory/${run.id}`, "_blank");

                    // Track successful collab creation
                    trackFeatureUsage("Collab", "created", {
                      experiment_id: run.id,
                      experiment_name: run.question || "Untitled Experiment",
                    });

                    // Reset creating state after successful creation
                    setIsCreatingCollab(false);
                  } catch (error) {
                    console.error("Error saving to Firestore:", error);
                    alert("Failed to save collaboratory. Please try again.");
                    setIsCreatingCollab(false);
                  }
                }}
              >
                <div className="flex flex-row gap-2 items-center w-fit cursor-pointer">
                  {isCreatingCollab ? (
                    <>
                      <Spinner className="text-white" size={16} />
                      <p className="w-fit">Creating...</p>
                    </>
                  ) : (
                    <p className="w-fit">Collab</p>
                  )}
                </div>
              </div>
            ),
          },
        ]
      : []),

    // {
    //   customButton: (
    //     <a
    //       href={`/ideation/${run.id}`}
    //       onMouseDown={(e) => {
    //         if (run.state === "running") {
    //           e.preventDefault();
    //         }
    //       }}
    //     >
    //       <div
    //         className="flex py-[9.5px] px-4 justify-center items-center gap-2 border border-solid rounded-xl border-card-border bg-white hover:bg-secondary-grey shadow-sm"
    //         style={{
    //           opacity: run.state === "running" ? 0.5 : 1,
    //           pointerEvents: run.state === "running" ? "none" : "auto",
    //         }}
    //       >
    //         <div className="flex flex-row gap-2 items-center w-fit">
    //           <p className="w-fit">
    //             {run.failed ? "Retry" : "Replicate Experiment"}
    //           </p>
    //         </div>
    //       </div>
    //     </a>
    //   ),
    // },

    {
      customButton:
        run.state === "running" || run.sample_size === 0 ? (
          <div></div>
        ) : (
          <a
            href={`/insights/${run.id}`}
            target="_blank"
            rel="noopener noreferrer"
            onMouseDown={(e) => {
              if (run.state === "running") {
                e.preventDefault();
              }
            }}
          >
            <div
              className={buttonStyles}
              style={{
                opacity: run.state === "running" ? 0.5 : 1,
                pointerEvents: run.state === "running" ? "none" : "auto",
              }}
            >
              <div className="flex flex-row gap-2 items-center w-fit">
                <Sparkles />
                <p className="w-fit">Analytics Studio</p>
              </div>
            </div>
          </a>
        ),
    },
  ];

  return (
    <>
      {
        <>
          <OverviewCard
            tabDetails={tabDetails}
            cardDetailTop={cardDetailTop}
            collapsed={collapsed}
            customActionDetails={customActionDetails}
          />
          <SubscribeModal
            showModal={
              showSubscribeModal &&
              roles &&
              !roles.includes("employee") &&
              !roles.includes("customer")
            }
            setShowModal={setShowSubscribeModal}
          />
        </>
      }
    </>
  );
};

export default ExperimentOverviewCard;
