import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  ChevronLeft,
  Download,
  Share2,
  <PERSON><PERSON>pR<PERSON>,
  Users,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import HeaderClient from "./HeaderClient";
import { SurveyResultsChart } from "@/components/SurveyResultsChart";
import { NetSentimentChart } from "@/components/NetSentimentChart";
import { getAccessToken } from "@auth0/nextjs-auth0";
import ConceptImage from "./ConceptImage";

function getS3ImageUrl(imageName: string): string {
  const region = process.env.AWS_REGION || "us-east-1";
  const bucketName =
    process.env.AWS_S3_BUCKET_NAME || "subconscious-ai-uploads";

  if (!imageName) {
    return "/brain_logo.png";
  }

  const hostname =
    region === "us-east-1"
      ? `${bucketName}.s3.amazonaws.com`
      : `${bucketName}.s3.${region}.amazonaws.com`;

  return `https://${hostname}/${imageName}`;
}

async function ConceptTestPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const { accessToken } = await getAccessToken();
  console.log("🚀 ~ accessToken:", accessToken)

  if (!accessToken) {
    throw new Error("No access token found");
  }

  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${id}`,
    {
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    }
  );

  if (!res.ok) {
    throw new Error("Failed to fetch data");
  }

  const result = await res.json();
  const runDetails = result.run_details;

  const title = runDetails.run_name;
  const createdAt = runDetails.start_time;
  const description =
    runDetails.summary["Experiment Mappings"].concept_description;
  const imageNameFromApi = runDetails.summary["Experiment Mappings"].image_name;
  const image = getS3ImageUrl(imageNameFromApi);
  const when = runDetails.configs.experiment_design.year;
  const whereName = runDetails.configs.experiment_design.country;
  const whereFlag =
    whereName === "United States of America (USA)"
      ? "http://purecatamphetamine.github.io/country-flag-icons/3x2/US.svg"
      : "";

  const surveyResultsData =
    runDetails.summary["Survey Analytics"].survey_results;

  const conceptStatements =
    runDetails.summary["Experiment Mappings"].concept_statements;

  const audienceSize =
    surveyResultsData && surveyResultsData.length > 0
      ? Object.values(surveyResultsData[0].responses).reduce(
          (sum: number, val: any) => sum + val,
          0
        )
      : 0;

  const demographics = [];
  const targetPopulation =
    runDetails.configs.experiment_design.target_population;

  if (targetPopulation.age) {
    demographics.push({
      name: "Age",
      value: `${targetPopulation.age[0]}-${targetPopulation.age[1]}`,
    });
  }
  if (targetPopulation.gender) {
    demographics.push({
      name: "Gender",
      value: targetPopulation.gender.join(", "),
    });
  }
  if (targetPopulation.education_level) {
    demographics.push({
      name: "Education",
      value: targetPopulation.education_level
        .map((ed: string) => ed.replace(/_/g, " "))
        .join(", "),
    });
  }
  if (targetPopulation.household_income) {
    demographics.push({
      name: "Income",
      value: `$${targetPopulation.household_income[0].toLocaleString()}-$${targetPopulation.household_income[1].toLocaleString()}`,
    });
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Top navigation bar */}
      <div className="sticky top-0 z-10 bg-background border-b border-border">
        <div className="container mx-auto px-4 sm:px-6 py-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Link
              href="/concept-testing/results"
              className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              <span>Back to Tests</span>
            </Link>
          </div>
          <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
            <HeaderClient />
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 container mx-auto px-4 sm:px-6 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-semibold text-text-dark">{title}</h1>
            <div className="flex items-center gap-2 mt-2 text-muted-foreground">
              <span>{formatDate(createdAt)}</span>
              <span className="inline-block h-1 w-1 rounded-full bg-muted-foreground"></span>
              <span className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {audienceSize} respondents
              </span>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="h-10 gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" className="h-10 gap-2">
              <Share2 className="h-4 w-4" />
              Share
            </Button>
          </div>
        </div>

        {/* Concept Details */}
        <div className="bg-white rounded-lg border border-input shadow-sm mb-8">
          <div className="p-6 border-b border-input">
            <h2 className="text-xl font-semibold text-text-default mb-1">
              Concept Details
            </h2>
            <p className="text-sm text-muted-foreground mb-6">
              Information about the tested concept
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Image */}
              <div>
                <h3 className="text-base font-medium mb-3">Concept Image</h3>
                <div className="relative aspect-square w-full overflow-hidden rounded-lg border border-input">
                  <ConceptImage
                    src={image}
                    alt="Concept"
                    className="object-cover"
                  />
                </div>
              </div>

              {/* Description and metadata */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-base font-medium mb-3">Description</h3>
                  <div className="p-4 bg-muted/20 border border-input rounded-lg">
                    <p className="text-text-default">{description}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium mb-2">When</h3>
                    <div className="p-3 bg-muted/20 rounded-md border border-input text-sm">
                      {when}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-2">Where</h3>
                    <div className="p-3 bg-muted/20 rounded-md border border-input text-sm flex items-center gap-2">
                      {whereFlag && (
                        <Image
                          src={whereFlag}
                          alt={`${whereName} flag`}
                          width={24}
                          height={16}
                          className="inline-block mr-1"
                        />
                      )}
                      {whereName}
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-base font-medium mb-3">
                    Target Audience
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    {demographics.map((demo, idx) => (
                      <div
                        key={idx}
                        className="flex flex-col p-3 bg-muted/20 rounded-md border border-input"
                      >
                        <span className="text-xs text-muted-foreground mb-1">
                          {demo.name}
                        </span>
                        <span className="text-sm font-medium">
                          {demo.value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Survey Results Charts */}
        <div className="bg-white rounded-lg border border-input shadow-sm mb-8">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-text-default mb-6">
              Survey Analysis
            </h2>
            <div className="space-y-8">
              <SurveyResultsChart
                data={surveyResultsData}
                conceptStatements={conceptStatements}
              />
              <NetSentimentChart
                data={surveyResultsData}
                conceptStatements={conceptStatements}
              />
            </div>
          </div>
        </div>

        {/* Call to action */}
        <div className="bg-primary/5 rounded-lg border border-primary/20 p-6 flex flex-col md:flex-row justify-between items-center gap-4">
          <div>
            <h3 className="text-lg font-semibold text-text-dark mb-1">
              Want to test another concept?
            </h3>
            <p className="text-muted-foreground">
              Create a new concept test and gather insights from your target
              audience.
            </p>
          </div>
          <Button className="whitespace-nowrap gap-2">
            Create New Test
            <ArrowUpRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export default ConceptTestPage;
