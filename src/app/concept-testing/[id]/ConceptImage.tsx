"use client";

import React, { useState } from "react";
import Image from "next/image";

interface ConceptImageProps {
  src: string;
  alt: string;
  className?: string;
}

export default function ConceptImage({
  src,
  alt,
  className,
}: ConceptImageProps) {
  const [imageSrc, setImageSrc] = useState(src);

  return (
    <Image
      src={imageSrc}
      alt={alt}
      fill
      className={className}
      onError={() => {
        setImageSrc("/brain_logo.png"); // Set fallback image on error
      }}
    />
  );
}
