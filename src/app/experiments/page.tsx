//@ts-nocheck
"use client";
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useRouter } from "next/navigation";
import Loading from "../_components/_ui/Loading";
import ExperimentOverviewCard from "../_components/_experiments/ExperimentOverviewCard";
import { Run } from "../_components/_experiments/types";
import { useUser, withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import SessionContext from "../_components/_util/SessionContext";
import filterRuns from "../api/util/filter_runs";
// import { NoExperimentIcon } from "../../../public/icons/NoExperimentIcon";
import {
  AlarmClock,
  ChevronUp,
  RefreshCw,
  Search,
  FileQuestion,
} from "lucide-react";
import { Header } from "../_components/_ui/NotificationCenter";
import CustomEventSource from "../utils/CustomEventSource";
import * as Sentry from "@sentry/react";
// import { ErrorMessage } from "@/app/utils/errorMessage";
// import responseData from "../_components/experimentData.json";
// import { Spinner } from "../primitives/Spinner";

import Confetti from "react-confetti";
import Paginator from "../_components/_ui/Paginator";
import axios from "axios";

// const LoadingSpinner = () => {
//   return (
//     <div className="flex flex-col items-center justify-center min-h-screen">
//       <div className="w-16 h-16 border-4 border-t-primary border-primary-dark rounded-full animate-spin"></div>
//       <p className="mt-4 text-lg text-gray-600">Loading experiments...</p>
//     </div>
//   );
// };

const fetcher = async (uri: string, userID: string | null) => {
  try {
    const response = await fetch(uri, {
      method: "POST",
      body: JSON.stringify({ userID: userID }),
    });
    if (!response.ok) {
      const error = new Error("Failed to fetch data");
      Sentry.captureException(error, {
        extra: { uri, userID, status: response.status },
      });
      throw error;
    }
    const data = await response.json();
    return data;
  } catch (error) {
    Sentry.captureException(error, {
      extra: { uri, userID },
    });
    throw error;
  }
};

const ITEMS_PER_PAGE = 10;

export default withPageAuthRequired(
  function ExperimentsPage() {
    const { user } = useUser();
    const router = useRouter();
    const [collapsed, setCollapsed] = useState(true);
    const { runs, setRuns } = useContext(SessionContext);
    const [eventSource, setEventSource] = useState(null);

    const [localRuns, setLocalRuns] = useState<Run[] | undefined>(undefined);
    const [activeTab, setActiveTab] = useState("myExperiments");
    const [filteredData, setFilteredData] = useState<any[]>([]);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [connectionActive, setConnectionActive] = useState(true);

    const [selectedDropdownItem, setSelectedDropdownItem] =
      useState("All Experiments");

    const [isLoading, setIsLoading] = useState(true);
    const [connectionError, setConnectionError] = useState(false);
    const [accessToken, setAccessToken] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");

    const [showConfetti, setShowConfetti] = useState(false);
    const [confettiExperimentId, setConfettiExperimentId] = useState<
      string | null
    >(null);

    const [queueCount, setQueueCount] = useState(0);

    useEffect(() => {
      const timer = setTimeout(() => {
        setDebouncedSearchQuery(searchQuery);
      }, 300);

      return () => clearTimeout(timer);
    }, [searchQuery]);

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const fetchAndUpdateData = async () => {
      setIsLoading(true);

      if (user?.sub) {
        try {
          const fetchRuns = await fetcher("/api/runs", user?.sub);

          const filteredRuns = filterRuns(fetchRuns, "conjoint");
          setLocalRuns(filteredRuns);
          setRuns(filteredRuns);

          let pendingExpRunId = localStorage.getItem("pendingExpRunId") || "";
          let recentExpId = filteredRuns.find(
            (run) => run.state == "finished"
          )?.id;

          // Check if we should show confetti for a newly finished experiment
          if (
            pendingExpRunId &&
            recentExpId &&
            pendingExpRunId === recentExpId
          ) {
            localStorage.setItem(
              "runningExperiments",
              Math.max(
                0,
                JSON.parse(localStorage.getItem("runningExperiments") || "0") -
                  1
              )
            );
          }

          let runningExperiments = JSON.parse(
            localStorage.getItem("runningExperiments")
          );

          // Find newly started experiments
          const newRunningExperiment = filteredRuns.find(
            (run) => run.state === "running"
          );

          // Update pendingExpRunId
          const previousPendingId = pendingExpRunId;
          pendingExpRunId = newRunningExperiment?.id;
          localStorage.setItem("pendingExpRunId", pendingExpRunId || "");

          // Show confetti for newly started experiments
          if (pendingExpRunId && pendingExpRunId !== previousPendingId) {
            // Get the list of experiments that have already shown confetti
            const shownConfettiExperiments = JSON.parse(
              localStorage.getItem("shownConfettiExperiments") || "[]"
            );

            // If this experiment hasn't shown confetti yet, show it
            if (!shownConfettiExperiments.includes(pendingExpRunId)) {
              setConfettiExperimentId(pendingExpRunId);
              setShowConfetti(true);

              // Add this experiment to the list of experiments that have shown confetti
              shownConfettiExperiments.push(pendingExpRunId);
              localStorage.setItem(
                "shownConfettiExperiments",
                JSON.stringify(shownConfettiExperiments)
              );
            }
          }

          const queueResponse = await axios.get("/api/tasks");
          const queueData = queueResponse.data;
          setQueueCount(queueData.length);
        } catch (error) {
          console.error("Error fetching runs:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    useEffect(() => {
      const fetchToken = async () => {
        try {
          const response = await fetch("/api/token");
          if (!response.ok) {
            throw new Error("Network response was not ok");
          }
          const data = await response.json();
          setAccessToken(data.accessToken);
        } catch (error) {
          console.error("Failed to fetch access token:", error);
          Sentry.captureException(error);
        }
      };

      fetchToken();
    }, []);

    useEffect(() => {
      const setupEventSource = () => {
        if (user && accessToken) {
          const newEventSource = new CustomEventSource(
            `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/notifications/events`,
            {
              headers: {
                Authorization: `Bearer ${accessToken}`,
              },
            }
          );

          const eventHandler = (event) => {
            try {
              const data = JSON.parse(event.data);
            } catch (error) {
              console.error("Failed to parse event data:", error);
              Sentry.captureException(error, {
                extra: {
                  message: "Malformed event data",
                  data: event.data,
                },
              });
            }
            fetchAndUpdateData();
          };

          newEventSource.addEventListener("started", eventHandler);
          newEventSource.addEventListener("finished", eventHandler);
          newEventSource.addEventListener("crashed", eventHandler);

          newEventSource.addEventListener("connection-status", (event) => {
            const status = JSON.parse(event.data);
            setConnectionActive(!status.reconnecting);
          });

          newEventSource.onerror = (error) => {
            Sentry.captureException(error, {
              extra: { message: "EventSource failed. Reconnecting..." },
            });
            setTimeout(() => {
              setupEventSource(); // Retry connecting
            }, 3000);
          };

          setEventSource(newEventSource);
        }
      };

      if (user && accessToken) {
        fetchAndUpdateData();
        setupEventSource();
      }

      return () => {
        if (eventSource) {
          eventSource.close();
        }
      };
    }, [user, accessToken]);

    const searchExperiments = (query: string, experiments: Run[]): Run[] => {
      if (!experiments?.length) return experiments;
      if (!query.trim()) return experiments;

      // Convert query to lowercase and split into terms for multi-word search
      const searchTerms = query.toLowerCase().trim().split(/\s+/);

      return experiments.filter((experiment) => {
        // Create a searchable string combining all relevant fields
        const searchableText = [
          // Basic info
          experiment.name,
          experiment.id,
          experiment.state,
          experiment.created_at,
          experiment.confidence,
          experiment.question,
          experiment.survey_prompt,

          // Numeric values converted to string
          experiment.r_squared?.toString(),
          experiment.sample_size?.toString(),
          experiment.total_number_of_tasks?.toString(),
          experiment.task_count?.toString(),

          // Status info
          experiment.failed ? "failed" : "success",
          experiment.is_private ? "private" : "public",
        ]
          .filter(Boolean) // Remove null/undefined values
          .join(" ")
          .toLowerCase();

        return searchTerms.every((term) => searchableText.includes(term));
      });
    };

    const filterExperiments = useCallback(
      (runs: Run[]) => {
        if (!runs) return [];

        let filtered = searchExperiments(debouncedSearchQuery, runs);

        if (activeTab === "communityExperiments") {
          filtered = filtered.filter((run) => !run.is_private);
        } else {
          if (selectedDropdownItem === "Your Public Experiments") {
            filtered = filtered.filter((run) => !run.is_private);
          } else if (selectedDropdownItem === "Private Experiments") {
            filtered = filtered.filter((run) => run.is_private);
          }
        }

        return filtered;
      },
      [activeTab, selectedDropdownItem, debouncedSearchQuery, searchExperiments]
    );

    const handleTabClick = useCallback((tab: string) => {
      setActiveTab(tab);
      setCurrentPage(1);
    }, []);

    const handleShowAllExperiments = useCallback(() => {
      setCollapsed(!collapsed);
    }, [collapsed]);

    const handleDropdownItemClick = useCallback((item: string) => {
      setSelectedDropdownItem(item);
      setCollapsed(true);
      setCurrentPage(1);
    }, []);

    const filteredLocalRuns = useMemo(
      () => (localRuns ? filterExperiments(localRuns) : []),
      [localRuns, filterExperiments]
    );

    const filteredRuns = useMemo(
      () => filterExperiments(runs),
      [runs, filterExperiments]
    );

    // const myExperimentsCount = useMemo(
    //   () => filteredRuns.filter((run) => run.amce_filename).length,
    //   [filteredRuns]
    // );

    const handleRefreshClick = useCallback(() => {
      fetchAndUpdateData();
    }, [fetchAndUpdateData]);

    const quickRefresh = useCallback(() => {
      return fetchAndUpdateData();
    }, [fetchAndUpdateData]);

    const handlePrivacyToggle = (id: string, isPrivate: boolean) => {
      setLocalRuns((prevLocalRuns) =>
        prevLocalRuns.map((run) =>
          run.id === id ? { ...run, is_private: isPrivate } : run
        )
      );
      setRuns((prevRuns) =>
        prevRuns.map((run) =>
          run.id === id ? { ...run, is_private: isPrivate } : run
        )
      );
    };

    // ------------Pagination----------------------
    const paginatedData = filteredData.slice(
      (currentPage - 1) * ITEMS_PER_PAGE,
      currentPage * ITEMS_PER_PAGE
    );
    useEffect(() => {
      if (runs.length > 0) {
        setFilteredData(runs);
      }
    }, [runs, paginatedData]);

    // const totalPages = Math.ceil(filteredData.length / ITEMS_PER_PAGE);

    const handlePageChange = (page: number) => {
      setCurrentPage(page);
    };

    const ReplayTutorials = () => {
      if (window.pendo && window.pendo.isReady) {
        window.pendo.showGuideById("kpKqPpqjYOEUf_o5hXy9GDtUeRM");
      }
    };

    useEffect(() => {
      if (showConfetti) {
        const timer = setTimeout(() => {
          setShowConfetti(false);
          setConfettiExperimentId(null);
        }, 2500);
        return () => clearTimeout(timer);
      }
    }, [showConfetti]);

    return (
      <div className="z-10 py-8 px-10 w-full flex flex-col font-inter">
        {showConfetti && <Confetti gravity={0.3} />}

        <div className="flex justify-between">
          <h1 className="text-text-dark font-medium text-3xl pb-6">
            Experiment Results
          </h1>
          <div className="flex items-center gap-2">
            <div
              className="flex cursor-pointer h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
              onClick={handleRefreshClick}
            >
              <RefreshCw className="w-7 h-6 text-[#868e90]" />
            </div>
            <button
              onClick={ReplayTutorials}
              className="flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
            >
              Show Tutorial
            </button>
            <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
              <Header />
            </div>
          </div>
        </div>

        <div className="flex w-full justify-between items-center">
          <div className="flex border-b relative border-[#D0D5DD] h-8 text-sm font-semibold">
            <div
              className={`cursor-pointer ${
                activeTab === "myExperiments"
                  ? "border-b text-[#312E81] border-[#312E81]"
                  : "text-[#667085]"
              }`}
              onClick={() => handleTabClick("myExperiments")}
            >
              Your Experiments
            </div>
          </div>
          {activeTab === "myExperiments" && (
            <div className="min-w-fit flex flex-col relative cursor-pointer">
              <div
                className="flex items-center justify-between gap-3 bg-white border-[#D0D5DD] w-full border px-3 py-2 boxshadow-allexps rounded-lg"
                onClick={handleShowAllExperiments}
              >
                {selectedDropdownItem}
                <ChevronUp
                  className={`h-6 w-6 text-chevron transition-all duration-300 ${
                    collapsed ? "rotate-180" : "rotate-0"
                  }`}
                />
              </div>
              {!collapsed && (
                <div className="flex w-full flex-col px-[6px] py-1 absolute boxshadow-dropdown top-12 rounded-lg z-10 bg-white">
                  <div
                    className={`flex justify-start rounded-md w-full px-2 py-3 cursor-pointer ${
                      selectedDropdownItem === "All Experiments"
                        ? "bg-[#EFF0F5]"
                        : ""
                    }`}
                    onClick={() => handleDropdownItemClick("All Experiments")}
                  >
                    All Experiments
                  </div>
                  <div
                    className={`flex justify-start px-2 w-full py-3 cursor-pointer ${
                      selectedDropdownItem === "Your Public Experiments"
                        ? "bg-[#EFF0F5]"
                        : ""
                    }`}
                    onClick={() =>
                      handleDropdownItemClick("Your Public Experiments")
                    }
                  >
                    Your Public Experiments
                  </div>
                  <div
                    className={`flex justify-start px-2 w-full py-3 cursor-pointer ${
                      selectedDropdownItem === "Private Experiments"
                        ? "bg-[#EFF0F5]"
                        : ""
                    }`}
                    onClick={() =>
                      handleDropdownItemClick("Private Experiments")
                    }
                  >
                    Private Experiments
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {isLoading ? (
          <div className="flex-1 flex items-center justify-center min-h-[calc(100vh-250px)]">
            <Loading />
          </div>
        ) : (
          <>
            {localRuns !== undefined &&
              localRuns.length === 0 &&
              runs.length === 0 &&
              queueCount === 0 && (
                <div className="flex items-center justify-center mt-4">
                  <div className="flex flex-col gap-4 w-full py-24 items-center text-center border border-card-border rounded-xl bg-white">
                    <FileQuestion className="w-16 h-16 text-gray-400" />
                    <p className="text-text-dark font-medium text-2xl">
                      You have not created any experiments yet.
                    </p>
                    <button
                      className="flex bg-[#504D9A] text-white font-inter font-medium text-lg px-4 py-2 rounded-xl items-center justify-center"
                      onMouseDown={() => {
                        router.push(`/ideation`);
                      }}
                    >
                      <div className="flex w-fit gap-2 items-center px-2">
                        <p className="whitespace-nowrap">
                          Run your first Experiment
                        </p>
                      </div>
                    </button>
                  </div>
                </div>
              )}

            {!isLoading &&
              runs.length === 0 &&
              localRuns !== undefined &&
              localRuns.length !== 0 && (
                <div className="flex flex-col gap-4 w-full justify-center items-center text-center">
                  <p className="font-roboto text-text-dark text-xl font-normal">
                    Please wait while your experiment finishes executing.
                  </p>
                </div>
              )}

            {localRuns && localRuns.length >= 5 && (
              <div className="relative flex items-center w-full mt-5 mb-4">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by experiment name, question, ID (partial ok), Date, Respondents(250), tasks (5), sample size (1250), Variance explained (71), confidence (High/Reasonable), private/public, model (GPT4), state (finished/failed)..."
                  className="w-full px-4 py-2 pl-10 border rounded-lg
                focus:outline-none focus:ring-2 focus:ring-primary-dark
                bg-white transition-all duration-200"
                  spellCheck="false"
                  autoComplete="off"
                />
                <Search className="absolute left-3 w-4 h-4 text-gray-400" />
              </div>
            )}

            {queueCount > 0 && (
              <div className="border mt-4 border-blue-500 text-blue-700 rounded-md px-3 py-2 bg-blue-100 font-semibold mb-2 flex flex-row items-center gap-3">
                <div>
                  <AlarmClock />
                </div>
                <div className="flex flex-col">
                  <div className="py-1">
                    Experiments in Queue ({queueCount}). It will start
                    shortly...
                  </div>
                </div>
              </div>
            )}

            <div className="mb-4 mt-4">
              {filteredLocalRuns && filteredLocalRuns.length !== 0 && (
                <div className="flex flex-col w-full gap-4">
                  {filteredLocalRuns
                    .filter(
                      (run: Run) => run.state === "running" && !run.question
                    )
                    .map((run: Run) => (
                      <div key={run.id}>
                        <ExperimentOverviewCard
                          run={run}
                          runCount={filteredLocalRuns.length}
                          onPrivacyToggle={handlePrivacyToggle} // pass the callback
                          quickRefresh={quickRefresh}
                        />
                      </div>
                    ))}
                </div>
              )}
            </div>

            {filteredRuns.length !== 0 && (
              <div className="flex flex-col w-full gap-4">
                {filteredRuns
                  .filter((run: Run) => run.state !== "running")
                  .slice(
                    (currentPage - 1) * ITEMS_PER_PAGE,
                    currentPage * ITEMS_PER_PAGE
                  )
                  .map((run: Run) => {
                    return (
                      <div key={run.id}>
                        <ExperimentOverviewCard
                          run={run}
                          runCount={filteredRuns.length}
                          onPrivacyToggle={handlePrivacyToggle}
                          quickRefresh={quickRefresh}
                        />
                      </div>
                    );
                  })}
              </div>
            )}

            {filteredRuns.length > ITEMS_PER_PAGE && (
              <div className="flex justify-center items-center mt-4">
                <Paginator
                  page={currentPage}
                  setPage={handlePageChange}
                  totalPage={Math.ceil(filteredRuns.length / ITEMS_PER_PAGE)}
                />
              </div>
            )}
          </>
        )}
      </div>
    );
  },
  {
    returnTo: "/experiments",
  }
);
