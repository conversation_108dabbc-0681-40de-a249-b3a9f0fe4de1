"use client";
import { useUser, withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import Cookies from "js-cookie";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState, useCallback, useMemo } from "react";
import countries from "../../../public/data/countries.json";
import ExperimentCreationContext, {
  CausalQuestion,
  QueryHistory,
} from "../_components/_ideation/ExperimentCreationContext";
import { ValidationProvider } from "../_components/_ideation/_who/contexts/ValidationContext";
import {
  BrandAttributeCombination,
  Country,
  CreateExperimentRequest,
  DisplayAttribute,
  DisplayTrait,
  FileState,
  LLMModel,
  Persona,
  PopulationTraits,
  TraitCategory,
} from "../_components/_ideation/objects";
import ProgressIndicator from "../_components/_ideation/ProgressIndicator";
import WhatComponent from "../_components/_ideation/WhatComponent";
import WhenWhereComponent from "../_components/_ideation/WhenWhereComponent";
import WhoComponent from "../_components/_ideation/WhoComponent";
import SubscribeModal from "../_components/_payments/SubscribeModal";
import { Header } from "../_components/_ui/NotificationCenter";
import { LogRunExperimentEvent } from "../_components/_util/Analytics";
import WhyComponent from "../_components/_ideation/WhyComponent";
import * as Sentry from "@sentry/nextjs";
import IdeationContextWrapper from "../_components/_ideation/IdeationContextWrapper";
import { useSubscription } from "@/app/hooks/useSubscription";

const fetcher = async (uri: string, body: CreateExperimentRequest) => {
  const response = await fetch(uri, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(body),
  });
  const data = await response.json();
  return data;
};

const models: LLMModel[] = [
  { name: "gpt4" },
  { name: "gpt3" },
  { name: "gcp-sonnet" },
  { name: "gcp-gemini" },
];

const defaultCountry = countries.find(
  (country) => country.name === "United States of America (USA)"
)!;

export default withPageAuthRequired(
  function IdeationPage() {
    const [steps] = useState([
      { name: "Why", status: "current" },
      { name: "When/where", status: "upcoming" },
      { name: "Who", status: "upcoming" },
      { name: "What", status: "upcoming" },
    ]);

    const { subscriptionStatus, roles, isLoading } = useSubscription();
    const router = useRouter();
    const { user } = useUser();

    // Core experiment data
    const [question, setQuestion] = useState("");
    const [focus, setFocus] = useState("engineering");
    const [when, setWhen] = useState(new Date().getFullYear().toString());
    const [where, setWhere] = useState<Country>(defaultCountry);
    const [selectedLlmModel, setSelectedLlmModel] = useState<LLMModel>(
      models[0]
    );

    // UI state
    const [isPrivate, setIsPrivate] = useState(false);
    const [showSubscribeModal, setShowSubscribeModal] =
      useState<boolean>(false);
    const [currStep, setCurrStep] = useState(0);
    const [previousStep, setPreviousStep] = useState<number>(-1);
    const [whoSteps, setWhoSteps] = useState<"first" | "second" | "third">(
      previousStep !== 3 ? "first" : "third"
    );

    // Data state
    const [personas, setPersonas] = useState<Persona[]>([]);
    const [traits, setTraits] = useState<Record<string, string[]>>({});
    const [activeSpecialist, setActiveSpecialist] = useState("");
    const [displayTraits, setDisplayTraits] = useState<DisplayTrait[]>([]);
    const [displayAttributes, setDisplayAttributes] = useState<
      DisplayAttribute[]
    >([]);
    const [
      realWorlBrandAttributeCombinations,
      setRealWorldBrandAttributeCombinations,
    ] = useState<BrandAttributeCombination[]>([]);
    const [selectedState, setSelectedState] = useState<string | null>(null);
    const [selectedSection, setSelectedSection] = useState<
      "characteristics" | "personas"
    >("characteristics");
    const [specialistTraits, setSpecialistTraits] = useState<
      Record<string, DisplayTrait[]>
    >({});

    // File and validation state
    const [fileState, setFileState] = useState<FileState>({
      file: null,
      data: null,
      error: null,
    });
    const [productExists, setProductExists] = useState(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [userRunsCount, setUserRunsCount] = useState<number>(0);
    const [validatedQuestions, setValidatedQuestions] = useState<Set<string>>(
      new Set()
    );

    // Query history state
    const [causalQuestions, setCausalQuestions] = useState<CausalQuestion[]>(
      []
    );
    const [queryHistory, setQueryHistory] = useState<QueryHistory[]>([]);
    const [lastSubmittedQuery, setLastSubmittedQuery] = useState("");
    const [showHistory, setShowHistory] = useState(false);

    const [populationTraits, setPopulationTraits] = useState<PopulationTraits>({
      state: null,
      age: [18, 95],
      household_income: [0, 371000],
      gender: ["Female", "Male"],
      education_level: [
        "High School Diploma",
        "High School but no diploma",
        "Some College",
        "Less than high school",
        "Bachelors",
        "Masters",
        "Associates",
        "PhD",
      ],
      number_of_children: ["0", "1", "2", "4+", "3"],
      racial_group: [
        "White",
        "African American",
        "Mixed race",
        "Asian or Pacific Islander",
        "Other race",
      ],
    });

    // Memoize expensive calculations
    const canRunExperiments = useMemo(
      () =>
        userRunsCount < 2 ||
        subscriptionStatus === "Active" ||
        (roles && (roles.includes("employee") || roles.includes("customer"))),
      [userRunsCount, subscriptionStatus, roles]
    );

    // Memoize transform traits function
    const transformTraits = useCallback((traits: any): DisplayTrait[] => {
      const result: DisplayTrait[] = [];

      Object.entries(traits).forEach(([category, categoryTraits]) => {
        if (typeof categoryTraits === "object" && categoryTraits !== null) {
          Object.entries(categoryTraits as Record<string, string[]>).forEach(
            ([trait, values]) => {
              result.push({
                title: trait.charAt(0).toUpperCase() + trait.slice(1),
                active: false,
                values: Array.isArray(values)
                  ? values.map(
                      (item) => item.charAt(0).toUpperCase() + item.slice(1)
                    )
                  : [],
                category: category as TraitCategory,
              });
            }
          );
        }
      });

      return result;
    }, []);

    // Memoize transformed display traits
    const displayTraitsFromTraits = useMemo(() => {
      if (!traits || Object.keys(traits).length === 0) return [];
      try {
        return transformTraits(traits);
      } catch (error) {
        console.error("Error transforming traits:", error);
        return [];
      }
    }, [traits, transformTraits]);

    // Update display traits when transformed traits change
    useEffect(() => {
      if (displayTraitsFromTraits.length > 0 && displayTraits.length === 0) {
        setDisplayTraits(displayTraitsFromTraits);
      }
    }, [displayTraitsFromTraits, displayTraits.length]);

    // Load question from cookie on mount
    useEffect(() => {
      const questionFromCookie = Cookies.get("storedQuestion");
      if (questionFromCookie) {
        setQuestion(questionFromCookie);
        Cookies.remove("storedQuestion");
      }
    }, []);

    // Fetch user runs count
    const fetchUserRunsCount = useCallback(async () => {
      try {
        if (user?.sub) {
          const response = await fetch("/api/runs/user-runs-count");
          if (response.ok) {
            const data = await response.json();
            setUserRunsCount(parseInt(data.runs_count));
          } else {
            throw new Error("Failed to fetch user runs count");
          }
        }
      } catch (error) {
        console.error("Error fetching user runs count:", error);
        Sentry.captureException(error);
      }
    }, [user?.sub]);

    useEffect(() => {
      if (user) {
        fetchUserRunsCount();
      }
    }, [user, fetchUserRunsCount]);

    // Navigation handlers
    const onBack = useCallback(() => {
      setPreviousStep(currStep);
      setCurrStep(currStep - 1);
    }, [currStep]);

    const completeWhy = useCallback(() => {
      steps[0].status = "complete";
      if (steps[1].status !== "complete") steps[1].status = "current";
      setPreviousStep(currStep);
      setCurrStep(1);
    }, [currStep, steps]);

    const completeWhenWhere = useCallback(() => {
      steps[1].status = "complete";
      if (steps[2].status !== "complete") steps[2].status = "current";
      setPreviousStep(currStep);
      setCurrStep(2);

      // Set WHO steps based on country selection
      if (where.name !== "United States of America (USA)") {
        setWhoSteps("third");
      } else {
        setWhoSteps("first");
      }
    }, [currStep, steps, where.name]);

    // Handle country selection changes (user interaction)
    const handleCountryChange = useCallback((newCountry: Country) => {
      setWhere(newCountry);

      // Reset WHO steps based on new country selection
      if (newCountry.name === "United States of America (USA)") {
        setWhoSteps("first");
      } else {
        setWhoSteps("third");
      }
    }, []);

    const completeWho = useCallback(() => {
      steps[2].status = "complete";
      if (steps[3].status !== "complete") steps[3].status = "current";
      setPreviousStep(currStep);

      if (!populationTraits || Object.keys(populationTraits).length === 0) {
        console.warn("Population traits were undefined, using default values");
      }

      setCurrStep(3);
    }, [currStep, steps, populationTraits]);

    // Privacy handlers
    const setExperimentPrivate = useCallback(() => {
      setIsPrivate(true);
    }, []);

    const setExperimentPublic = useCallback(() => {
      setIsPrivate(false);
    }, []);

    // Build run request
    const buildRunRequest = useCallback(async () => {
      if (!question || !when || !where || isPrivate === null || undefined) {
        console.error("One or more fields are missing or invalid.");
        setErrorMessage("One or more fields are missing or invalid.");
        return false;
      }

      const requestBody: CreateExperimentRequest = {
        question: question,
        year: when,
        country: where!.name,
        is_private: isPrivate,
        experiment_type: "conjoint",
        state:
          where.name === "United States of America (USA)"
            ? selectedState
            : null,
        population_traits: displayTraits,
        displayAttributes: displayAttributes,
        realworld_products: realWorlBrandAttributeCombinations,
        ...(personas.length > 0
          ? { external_personas: personas }
          : { target_population: populationTraits }),

        concept_description: "",
        concept_statements: [],
        image_name: "",
      };

      try {
        return await fetcher("/api/experiments", requestBody);
      } catch (error) {
        console.error("Error in buildRunRequest:", error);
        return false;
      }
    }, [
      question,
      when,
      where,
      isPrivate,
      displayTraits,
      displayAttributes,
      realWorlBrandAttributeCombinations,
      personas,
      populationTraits,
      selectedState,
    ]);

    // Section change handler
    const handleSectionChange = useCallback(
      (section: "characteristics" | "personas") => {
        if (section === "characteristics") {
          setPersonas([]);
        }
        setSelectedSection(section);
      },
      []
    );

    // Run experiment handler
    const onRunExperiment = useCallback(async () => {
      if (!canRunExperiments) {
        setShowSubscribeModal(true);
        return;
      }

      if (isPrivate !== null || undefined) {
        let res = await buildRunRequest();
        if (res) {
          if (typeof window !== "undefined") {
            let runningExperiments =
              JSON.parse(localStorage.getItem("runningExperiments") || "0") + 1;
            localStorage.setItem(
              "runningExperiments",
              JSON.stringify(runningExperiments)
            );
            localStorage.setItem("pendingExpRunId", res.wandb_run_id);
          }
          <LogRunExperimentEvent email={user?.email} />;
          router.push("/experiments");
        }
      }
    }, [canRunExperiments, isPrivate, buildRunRequest, user?.email, router]);

    // Modal handlers
    const handleShowSubscribeModal = useCallback(() => {
      setShowSubscribeModal(true);
    }, []);

    const ReplayTutorials = useCallback(() => {
      if (window.pendo && window.pendo.isReady) {
        window.pendo.showGuideById("naq23DrKfGmhtz1HHf6MQpQ5jOg");
      }
    }, []);

    // Memoize context value to prevent unnecessary re-renders
    const contextValue = useMemo(
      () => ({
        question,
        focus,
        activeSpecialist,
        displayTraits,
        populationTraits,
        displayAttributes,
        realWorlBrandAttributeCombinations,
        when,
        where,
        selectedLlmModel,
        selectedState,
        validatedQuestions,
        personas,
        setPersonas,
        specialistTraits,
        setSpecialistTraits,
        setValidatedQuestions,
        setSelectedState,
        setQuestion,
        setFocus,
        setActiveSpecialist,
        setPopulationTraits,
        setDisplayTraits,
        setDisplayAttributes,
        setRealWorldBrandAttributeCombinations,
        setWhen,
        setWhere: handleCountryChange,
        setSelectedLlmModel,
        productExists,
        setProductExists,
        selectedSection,
        setSelectedSection: handleSectionChange,
        fileState,
        setFileState,
        causalQuestions,
        queryHistory,
        lastSubmittedQuery,
        showHistory,
        setCausalQuestions,
        setQueryHistory,
        setLastSubmittedQuery,
        setShowHistory,
      }),
      [
        question,
        focus,
        activeSpecialist,
        displayTraits,
        populationTraits,
        displayAttributes,
        realWorlBrandAttributeCombinations,
        when,
        where,
        selectedLlmModel,
        selectedState,
        validatedQuestions,
        personas,
        specialistTraits,
        productExists,
        selectedSection,
        handleSectionChange,
        handleCountryChange,
        fileState,
        causalQuestions,
        queryHistory,
        lastSubmittedQuery,
        showHistory,
      ]
    );

    return (
      <IdeationContextWrapper selectedState={selectedState}>
        <ValidationProvider initialValidationResults={null}>
          <ExperimentCreationContext.Provider value={contextValue}>
            {!canRunExperiments && !isLoading && (
              <div className="w-full bg-[#312E81] py-3 flex gap-2 justify-center items-center">
                <p className="text-white font-semibold text-sm">
                  Your free trials are complete.
                </p>
                <p className="text-white font-normal text-sm">
                  <span
                    className=" underline cursor-pointer"
                    onClick={handleShowSubscribeModal}
                  >
                    Upgrade your account
                  </span>{" "}
                  to keep innovating!{" "}
                </p>
              </div>
            )}

            <SubscribeModal
              showModal={showSubscribeModal}
              setShowModal={setShowSubscribeModal}
            />

            <div className="z-10 py-8 px-10 w-full flex flex-col font-inter h-full">
              <div className="flex justify-between">
                <h1
                  id="ideation-why-guide-1"
                  className="text-text-dark font-medium text-3xl pb-6"
                >
                  Create Experiment
                </h1>
                {currStep === 0 && (
                  <div
                    style={{ position: "relative", display: "inline-block" }}
                  >
                    <div className="flex gap-2">
                      <button
                        onClick={ReplayTutorials}
                        className="flex h-11 py-3 px-4 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm"
                      >
                        Show Tutorial
                      </button>
                      <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
                        <Header />
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div className="mb-10 flex justify-center w-full">
                <ProgressIndicator steps={steps} setCurrStep={setCurrStep} />
              </div>
              {/* CONTENT */}
              <div className="flex items-center justify-center pb-4">
                {currStep === 0 && (
                  <WhyComponent
                    setExperimentPublic={setExperimentPublic}
                    onComplete={completeWhy}
                    setTraits={setTraits}
                    onRunExperiment={onRunExperiment}
                    errorMessage={errorMessage}
                    setErrorMessage={setErrorMessage}
                  />
                )}
                {currStep === 1 && (
                  <WhenWhereComponent
                    countries={countries}
                    onBack={onBack}
                    onComplete={completeWhenWhere}
                    models={models}
                  />
                )}
                {currStep === 2 && (
                  <WhoComponent
                    onComplete={completeWho}
                    onBack={onBack}
                    existingYear={null}
                    existingCountry={null}
                    existingQuestion={null}
                    previousStep={previousStep}
                    setPopulationTraits={setPopulationTraits}
                    currentStep={whoSteps}
                    setCurrentStep={setWhoSteps}
                  />
                )}
                {currStep === 3 && (
                  <WhatComponent
                    setExperimentPrivate={setExperimentPrivate}
                    setExperimentPublic={setExperimentPublic}
                    onSubmit={onRunExperiment}
                    onBack={onBack}
                    canRunExperiment={canRunExperiments}
                  />
                )}
              </div>
            </div>
          </ExperimentCreationContext.Provider>
        </ValidationProvider>
      </IdeationContextWrapper>
    );
  },
  { returnTo: "/ideation" }
);
